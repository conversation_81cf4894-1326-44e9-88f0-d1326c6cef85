import React, {Component} from 'react';
import { <PERSON> } from "react-router-dom";
import { withRouter } from "react-router";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import { Modal, Form } from 'react-bootstrap';
import { connect } from 'react-redux';
import { addToCart2 } from '../../Services/Actions/cart.actions';
import { login2 } from '../../Services/Actions/user.actions';


class ProductDetailsListAreaComp extends Component {
    constructor(props) {
        super(props);
        this.state = {
            customerId: this.props.customerId,
            isProductDetailsError: null,
            isProductsDetailsLoaded : false,
            productDetails: {},
            productQuantity: 1,
            productMultipleImages: {},
            nav1: "",
            nav2: "",
            slider1: "",
            slider2: "",
            errors: {}, errorMessage: '', apiErrorMessage: '', apiSuccessMessage: '', isQuanValid: true,
            username: '', password: '', loginSubmitted: false, loginCheck: false,
            firstname: '', lastname: '', email: '', telephone: '', regpassword: '',
            forgotPassLoader: 'hidden', loginLoader: 'hidden', registrationLoader: 'hidden', loginFormButtonDisabled : false,
            registrationSubmitted: false, forgotPasswordSubmitted: false, forgotPasswordApiSuccessResponse: '', forgotPasswordApiErrorResponse: '',
            loginFormShowHide: "", registrationFormShowHide: "hidden", forgotPasswordShowHide: "hidden",
            showNotifyModal: false, showLoginModal: false,
            notifyProductSubmitted: false, notifytoemail: this.props.userEmail, notifyProdLoader: "hidden", notifyApiSuccessResponse: "",
            ratereviewLoader: "hidden", ratereviewtitle: '', ratereviewdesc: '', customerRatedNumber: '', reviewApiSuccessResponse: '',
            showUpdateCartMsgModal: false, isCartAddError: false, cartErrorMessage: '', addToCartButtonnLoader: "hidden", addToCartButtonnCart: "", addToCartButtonDisabled: false, addToCartSuccessMsg: ''
        }
        this.handleOnQuantityChange = this.handleOnQuantityChange.bind(this);
        this.handleOnNotifyEmailInputChange = this.handleOnNotifyEmailInputChange.bind(this);
        this.handleOnNotifyProductSubmit = this.handleOnNotifyProductSubmit.bind(this);
        this.openNotifyPop = this.openNotifyPop.bind(this);
        this.closeNotifyPop = this.closeNotifyPop.bind(this);
        this.handleOnRateReviewInputChange = this.handleOnRateReviewInputChange.bind(this);
        this.handleOnRateAndReviewSubmit = this.handleOnRateAndReviewSubmit.bind(this);
        this.handleOnLoginInputChange = this.handleOnLoginInputChange.bind(this);
        this.handleOnProductLoginSubmit = this.handleOnProductLoginSubmit.bind(this);

        this.handleOnRegistrationInputChange = this.handleOnRegistrationInputChange.bind(this);
        this.handleOnRegistrationSubmit = this.handleOnRegistrationSubmit.bind(this);
        this.handleOnForgotPasswordSubmit = this.handleOnForgotPasswordSubmit.bind(this);

        this.openLoginPop = this.openLoginPop.bind(this);
        this.closeLoginPop = this.closeLoginPop.bind(this);
        this.cartUpdatePopupResOpen = this.cartUpdatePopupResOpen.bind(this);
        this.cartUpdatePopupResClose = this.cartUpdatePopupResClose.bind(this);
    }

    handleOnQuantityChange(e) {
        const { name, value } = e.target;
        const regex = /^[0-9\b]+$/;
        if (value === '' || regex.test(value)) {
            this.setState({ [name]: value });
        }
    }

    handleOnRegistrationInputChange(e) {
        const { name, value } = e.target;
        this.setState({ [name]: value });
    }

    handleQuantityValidation(){
        let getProductQuantity = this.state.productQuantity;
        let isQuanValid = true;

        if(!getProductQuantity){
           isQuanValid = false;
        }else{
            const re = /^[0-9\b]+$/;
            if (re.test(getProductQuantity) && getProductQuantity <= 100) {
                isQuanValid = true;
            }else{
                isQuanValid = false;
            }
        }
        this.setState({ isQuanValid: isQuanValid });
       return isQuanValid;
    }

    handleOnAddToCart(product_id, product_name, product_image, price){
        if(this.handleQuantityValidation()){
            this.setState({addToCartButtonnLoader: '', addToCartButtonnCart: "hidden", addToCartButtonDisabled: true, addToCartSuccessMsg: ''})
            const quantity = this.state.productQuantity;
            this.props.addToCart(product_id, product_name, product_image, price, quantity);

            setTimeout(()=> 
                this.setState({
                    isCartAddError: this.props.isCartAddError 
                },() => {
                    if(this.state.isCartAddError){
                        this.setState({cartErrorMessage: this.props.cartErrorMessage, addToCartButtonnLoader: 'hidden', addToCartButtonnCart: '', addToCartButtonDisabled: false});
                        this.cartUpdatePopupResOpen();
                        setTimeout(()=> 
                            this.cartUpdatePopupResClose(),
                        5000);
                    } else {
                        this.setState({addToCartSuccessMsg: quantity+' x "'+product_name+'" have been added to your cart.', cartErrorMessage: '', addToCartButtonnLoader: 'hidden', addToCartButtonnCart: '', addToCartButtonDisabled: false});
                        setTimeout(()=> 
                            this.setState({addToCartSuccessMsg: ''}),
                        5000);
                    }
                }),
            3000);
        }
    }

    componentDidMount() {
        let product_id = this.props.match.params.productId;

        const apiUrl = 'https://www.evisionstore.com/api/product/view.php';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({"product_id": product_id})
        };
        fetch(apiUrl, requestOptions)
        .then(res => res.json())
        .then(
            (result) => {
                this.setState({
                    productDetails: result.product_view,
                    productMultipleImages: result.multiple_images,
                    isProductsDetailsLoaded: true
                });
                console.log(result);
            },
            (error) => {
                this.setState({ isProductDetailsError: error });
            }
        )

        //this.setState({nav1: this.state.slider1});
        //this.setState({nav2: this.state.slider2});
    }

    componentDidUpdate(prevProps, prevState) {
        //this.setState({nav1: this.state.slider1});
        //this.setState({nav2: this.state.slider2});
    }

    openNotifyPop() {
        this.setState({showNotifyModal: true});
    }
    
    closeNotifyPop() {
        this.setState({showNotifyModal: false});
    }

    cartUpdatePopupResOpen() {
        this.setState({showUpdateCartMsgModal: true});
    }
    
    cartUpdatePopupResClose() {
        this.setState({showUpdateCartMsgModal: false});
    }

    handleOnNotifyEmailInputChange(e) {
        const { name, value } = e.target;
        this.setState({ [name]: value });
    }

    handleNotifyProductFormValidation(){
        let notifytoemail = this.state.notifytoemail;
        let errors = {};
        let notifyProductFormIsValid = true;
    
        if(typeof notifytoemail !== "undefined"){
           let lastAtPos = notifytoemail.lastIndexOf('@');
           let lastDotPos = notifytoemail.lastIndexOf('.');
    
           if (!(lastAtPos < lastDotPos && lastAtPos > 0 && notifytoemail.indexOf('@@') === -1 && lastDotPos > 2 && (notifytoemail.length - lastDotPos) > 2)) {
              notifyProductFormIsValid = false;
              errors["notifytoemail"] = "Your registered email id is not valid";
            }
        } else {
            notifyProductFormIsValid = false;
            errors["notifytoemail"] = "Please enter your registered email id";
        } 
    
       this.setState({errors: errors});
       return notifyProductFormIsValid;
    }

    handleOnNotifyProductSubmit(e){
        e.preventDefault();
        this.setState({ notifyProductSubmitted: true });

        if(this.handleNotifyProductFormValidation()){
            this.setState({ notifyProdLoader: "" });
            let customer_email = this.state.notifytoemail;
            let product_id = this.props.match.params.productId;

            const apiUrl = 'https://www.evisionstore.com/api/user/account/notifyproemail.php';
        
            fetch(apiUrl, {
                method: 'POST',
                body: JSON.stringify({
                    "customer_email": customer_email,
                    "product_id": product_id
                })
            })
            .then(res => res.json())
            .then(
                (result) => {
                    if(result.code===200){
                        console.log(result);
                        this.setState({
                            notifyApiSuccessResponse: result.message,
                            notifyProdLoader: "hidden"
                        });
                    }else{
                        this.setState({
                            notifyApiSuccessResponse: result.message,
                            notifyProdLoader: "hidden"
                        });
                    }
                },
                (error) => {
                    this.setState({ error });
                }
            )
        }
    }

    handleOnRateAndReviewClick(rateNumber) {
        this.setState({customerRatedNumber: rateNumber});
    }

    handleOnRateReviewInputChange(e) {
        const { name, value } = e.target;
        this.setState({ [name]: value });
    }

    handleRateReviewValidation(){
        let ratereviewtitle = this.state.ratereviewtitle;
        let ratereviewdesc = this.state.ratereviewdesc;
        let customerRatedNumber = this.state.customerRatedNumber;
        let errors = {};
        let rateReview = true;

        if(!customerRatedNumber){
            rateReview = false;
            errors["customerRatedNumber"] = "Please select one of product star rate";
        }

        if(!ratereviewtitle){
            rateReview = false;
            errors["ratereviewtitle"] = "Please enter review title";
        }

        if(!ratereviewdesc){
            rateReview = false;
            errors["ratereviewdesc"] = "Please enter review comments";
        }
        
        this.setState({errors: errors});
        return rateReview;
    }

    handleOnRateAndReviewSubmit(e){
        e.preventDefault();
        this.setState({ rateAndReviewSubmitted: true });
        //console.log(this.handleRateReviewValidation())
        if(this.handleRateReviewValidation()){
            
            this.setState({ ratereviewLoader: "" });
            let customer_id = this.state.customerId;
            let product_id = this.props.match.params.productId;
            let rating_number = this.state.customerRatedNumber;
            let review_title = this.state.ratereviewtitle;
            let review_comment = this.state.ratereviewdesc;

            const apiUrl = 'https://www.evisionstore.com/api/user/account/customer_review.php';
        
            fetch(apiUrl, {
                method: 'POST',
                body: JSON.stringify({
                    "customer_id": customer_id,
                    "product_id": product_id,
                    "rating_number": rating_number,
                    "review_title": review_title,
                    "review_comment": review_comment
                })
            })
            .then(res => res.json())
            .then(
                (result) => {
                    if(result.code===200){
                        console.log(result);
                        this.setState({
                            reviewApiSuccessResponse: result.message,
                            ratereviewLoader: "hidden",
                            customerRatedNumber: '',
                            ratereviewtitle: '',
                            ratereviewdesc: ''
                        });
                    }else{
                        this.setState({
                            reviewApiSuccessResponse: result.message,
                            ratereviewLoader: "hidden"
                        });
                    }
                },
                (error) => {
                    this.setState({ error });
                }
            )
        }
    }

    openLoginPop() {
        this.setState({showLoginModal: true});
    }
    
    closeLoginPop() {
        this.setState({showLoginModal: false});
    }

    handleOnLoginInputChange(e) {
        const { name, value } = e.target;
        this.setState({ [name]: value });
    }

    handleProductLoginFormValidation(){
        let username = this.state.username;
        let password = this.state.password;
        let errors = {};
        let loginFormIsValid = true;

        if(!username){
           loginFormIsValid = false;
           errors["username"] = "Email can not be empty";
        }
    
        if(typeof username !== "undefined"){
           let lastAtPos = username.lastIndexOf('@');
           let lastDotPos = username.lastIndexOf('.');
    
           if (!(lastAtPos < lastDotPos && lastAtPos > 0 && username.indexOf('@@') === -1 && lastDotPos > 2 && (username.length - lastDotPos) > 2)) {
              loginFormIsValid = false;
              errors["username"] = "Email is not valid";
            }
        }  

        if(!password){
            loginFormIsValid = false;
            errors["password"] = "Password can not be empty";
        }
    
       this.setState({errors: errors});
       return loginFormIsValid;
    }

    handleOnProductLoginSubmit(e){
        e.preventDefault();
        this.setState({ loginSubmitted: true });

        if(this.handleProductLoginFormValidation()) {
            this.setState({ loginLoader: "", loginFormButtonDisabled: true });
            const username = this.state.username;
            const password = this.state.password;
            const cartItems = this.props.cartData;
            this.props.logIn(username, password, cartItems);

            setTimeout(()=> 
                this.setState({
                    loginCheck: this.props.isLoggedIn 
                },() => {
                    if(this.state.loginCheck){
                        this.setState({apiSuccessMessage: 'You have successfully logged in to your account.', loginFormButtonDisabled: false, loginLoader: "hidden"});
                        setTimeout(()=> 
                            this.close(),
                        3000);
                    }
                    if(this.props.isError){
                        this.setState({apiErrorMessage: this.props.errorMessage, loginFormButtonDisabled: false, loginLoader: "hidden"})
                    }
                }),
            3000);
        }
    }

    handleRegistrationFormValidation(){
        let firstname = this.state.firstname;
        let lastname = this.state.lastname;
        let email = this.state.email;
        let telephone = this.state.telephone;
        let regpassword = this.state.regpassword;
        let errors = {};
        let registrationFormIsValid = true;

        if(!firstname){
            registrationFormIsValid = false;
            errors["firstname"] = "Please enter your first name";
        }

        if(!lastname){
            registrationFormIsValid = false;
            errors["lastname"] = "Please enter your last name";
        }

        if(!telephone){
            registrationFormIsValid = false;
            errors["telephone"] = "Please enter your phone number.";
        } else{
            if(telephone.length < 10){
                registrationFormIsValid = false;
                errors["telephone"] = "Please enter your valid phone number.";
            }
        }
    
        if(typeof email !== "undefined"){
           let lastAtPos = email.lastIndexOf('@');
           let lastDotPos = email.lastIndexOf('.');
    
           if (!(lastAtPos < lastDotPos && lastAtPos > 0 && email.indexOf('@@') === -1 && lastDotPos > 2 && (email.length - lastDotPos) > 2)) {
              registrationFormIsValid = false;
              errors["email"] = "Email id is not valid";
            }
        } else{
            registrationFormIsValid = false;
            errors["email"] = "Please enter your username.";
        }  

        if(!regpassword){
            registrationFormIsValid = false;
            errors["regpassword"] = "Password can not be empty";
        }else{
            if(regpassword.length < 3){
                registrationFormIsValid = false;
                errors["regpassword"] = "Please enter minimum 4 characters password.";
            }
        }
    
       this.setState({errors: errors});
       return registrationFormIsValid;
    }

    handleOnRegistrationSubmit(e){
        e.preventDefault();
        this.setState({ registrationSubmitted: true });

        if(this.handleRegistrationFormValidation()){
            this.setState({ registrationLoader: "", loginFormButtonDisabled: true });
            const first_name = this.state.firstname;
            const last_name = this.state.lastname;
            const email = this.state.email;
            const password = this.state.regpassword;
            const telephone = this.state.telephone;

            this.props.signup(first_name, last_name, email, password, telephone);

            setTimeout(()=> 
                this.setState({
                    loginCheck: this.props.isLoggedIn 
                },() => {
                    if(this.state.loginCheck){
                        this.setState({apiSuccessMessage: 'Your account has been successfully registered on Evision.', loginFormButtonDisabled: false, registrationLoader: "hidden"});
                        setTimeout(()=> 
                            this.close(),
                        2000);
                    }
                    if(this.props.isSignupError){
                        this.setState({apiErrorMessage: this.props.signupErrorMessage, loginFormButtonDisabled: false, registrationLoader: "hidden"})
                    }
                }),
            2000);
        }
    }

    handleForgotPassFormValidation(){
        let forgotpassemail = this.state.forgotpassemail;
        let errors = {};
        let forgotPasswordFormIsValid = true;
    
        if(typeof forgotpassemail !== "undefined"){
           let lastAtPos = forgotpassemail.lastIndexOf('@');
           let lastDotPos = forgotpassemail.lastIndexOf('.');
    
           if (!(lastAtPos < lastDotPos && lastAtPos > 0 && forgotpassemail.indexOf('@@') === -1 && lastDotPos > 2 && (forgotpassemail.length - lastDotPos) > 2)) {
              forgotPasswordFormIsValid = false;
              errors["forgotpassemail"] = "Your registered email id is not valid";
            }
        } else {
            forgotPasswordFormIsValid = false;
            errors["forgotpassemail"] = "Please enter your registered email id";
        } 
    
       this.setState({errors: errors});
       return forgotPasswordFormIsValid;
    }

    handleOnForgotPasswordSubmit(e){
        e.preventDefault();
        this.setState({ forgotPasswordSubmitted: true });

        if(this.handleForgotPassFormValidation()){
            this.setState({ forgotPassLoader: "", loginFormButtonDisabled: true });
            let forgotpassemail = this.state.forgotpassemail;

            const apiUrl = 'https://www.evisionstore.com/api/user/forgotpassword.php';
        
            fetch(apiUrl, {
                method: 'POST',
                body: JSON.stringify({
                    "email": forgotpassemail
                })
            })
            .then(res => res.json())
            .then(
                (result) => {
                    if(result.code===200){
                        this.setState({
                            forgotPasswordApiSuccessResponse: result.message,
                            forgotPassLoader: "hidden",
                            loginFormButtonDisabled: false
                        });
                    }else{
                        this.setState({
                            forgotPasswordApiErrorResponse: result.message,
                            forgotPassLoader: "hidden",
                            loginFormButtonDisabled: false
                        });
                    }
                },
                (error) => {
                    this.setState({ error, forgotPassLoader: "hidden", loginFormButtonDisabled: false });
                }
            )
        }
    }

    handleOnFormChange(buttonParam) {
        if(buttonParam==="registration") {
            this.setState({
                loginFormShowHide: 'hidden',
                registrationFormShowHide: '',
                forgotPasswordShowHide: 'hidden'
            });
        } else if(buttonParam==="login") {
            this.setState({
                loginFormShowHide: '',
                registrationFormShowHide: 'hidden',
                forgotPasswordShowHide: 'hidden'
            });
        } else if(buttonParam==="forgotpassword"){
            this.setState({
                loginFormShowHide: 'hidden',
                registrationFormShowHide: 'hidden',
                forgotPasswordShowHide: ''
            });
        }
    }

    render(){
        const { 
            isProductsDetailsLoaded, productDetails, productQuantity, 
            productMultipleImages, nav1, nav2, slider1, slider2, 
            notifyProductSubmitted, notifytoemail,
            ratereviewtitle, ratereviewdesc, rateAndReviewSubmitted
            } = this.state;
        const { username, password, loginSubmitted, loginCheck } = this.state;    
        const { firstname, lastname, email, telephone, regpassword, registrationSubmitted, forgotPasswordSubmitted, forgotpassemail } = this.state;
        //const notifytoemail = this.props.userEmail;
        const isLoggedIn = this.props.isLoggedIn;

        const apiError = {
            color: 'red', fontSize: '16px', background: '#fff9b0', padding: '0px 5px 2px 5px', fontWeight: 'normal', textAlign: 'center', border: '1px solid red', borderRadius: '3px'
        }

        const apiSuccess = {
            color: '#316403', fontSize: '16px', background: '#cbffb0', padding: '0px 5px 2px 5px', fontWeight: 'normal', textAlign: 'center', border: '1px solid #178603', borderRadius: '3px'
        }

        const settingsMain = {
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            fade: true,
            asNavFor: '.slider-nav'
        };
    
        const settingsThumbs = {
            slidesToShow: 3,
            slidesToScroll: 1,
            asNavFor: '.slider-for',
            dots: true,
            centerMode: true,
            swipeToSlide: true,
            focusOnSelect: true,
            centerPadding: '10px'
        };
        
        if(isProductsDetailsLoaded){
            //console.log(productDetails);
            return <>
                {productDetails.map((product, index) => (
                    <div key={index}>
                        <div className="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <div className="exzoom" id="exzoom">
                                <div className="exzoom_img_box">
                                    <div className="slider-wrapper">
                                        <Slider {...settingsMain} asNavFor={nav2}  >
                                            {productMultipleImages.map((slide) =>
                                                <div className="slick-slide exzoom_img_ul" key={slide.sort_order}>
                                                    <img className="slick-slide-image" src={slide.image_name} alt="" />
                                                </div>
                                            )}
                                        </Slider>
                                        <div className="thumbnail-slider-wrap">
                                        <Slider {...settingsThumbs} asNavFor={nav1}  >
                                            {productMultipleImages.map((slide) =>
                                                <div className="slick-slide" key={slide.sort_order}>
                                                    <img className="slick-slide-image" src={slide.image_name} alt="" />
                                                </div>
                                            )}
                                        </Slider>
                                        </div>
                                    </div>
                                </div>
                                <div className="exzoom_nav"></div>
                            </div>    
                        </div> 
                    
                    {/* <div className="col-lg-6 col-md-6 col-sm-12 col-xs-12">	
                        <div className="exzoom" id="exzoom">
                        <div className="exzoom_img_box">
                            <ul className="exzoom_img_ul">
                            <li><img src={product.product_image} alt="" /></li>
                            </ul>
                        </div>
                        <div className="exzoom_nav" />
                        <p className="exzoom_btn">
                            <Link to="#" className="exzoom_prev_btn"> « </Link>
                            <Link to="#" className="exzoom_next_btn"> » </Link>
                        </p>
                        </div>
                    </div> */}

                    <div className="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                        <div className="details-container">
                            <h2>{product.product_name}</h2>
                            <h3>{product.short_description}</h3>
                            <div className="link">
                                <Link to={`/product-category/${product.category_id}`}>Ver más de {product.category_name}</Link>&nbsp;&nbsp;|&nbsp;&nbsp;
                                <Link to={`/product-category/${product.category_id}`}>Ver más de {product.brand}</Link>&nbsp;&nbsp;|&nbsp;&nbsp;
                                <Link to="">Calificaciones y revisión</Link>
                            </div>
                            <div className="price">{product.currency} {product.price}</div>

                            {product.addtocart_option !== 0 ? (
                                <>
                                <div className="qty"> Qty 
                                    <input className={'quantity' + (!this.state.isQuanValid ? ' has-error' : '')} type="text" name="productQuantity" value={productQuantity} onChange={this.handleOnQuantityChange} /> 
                                </div>
                                <span className={this.state.addToCartSuccessMsg ? '' : 'hidden'} style={{color:'green', fontWeight:'normal', fontSize:'16px', marginBottom: '10px', border:'1px solid green', padding:'5px 10px', background:'#daffe0', float:'left', textAlign:'center'}}>
                                    <i className="fa fa-check" aria-hidden="true"></i> {this.state.addToCartSuccessMsg}
                                </span>
                                <button onClick={() => this.handleOnAddToCart(product.product_id, product.product_name, product.product_image, product.price)} disabled={this.state.addToCartButtonDisabled}>
                                    <i className={`fa fa-refresh fa-spin ${ this.state.addToCartButtonnLoader }`} aria-hidden="true"></i> 
                                    <i className={`fa fa-shopping-cart ${ this.state.addToCartButtonnCart }`} aria-hidden="true"></i> 
                                    &nbsp;{this.state.addToCartButtonDisabled ? 'Añadiendo' : 'Agregar al carrito'}
                                </button>
                                </>
                            ) : (
                                <button onClick={this.openNotifyPop} style={{margin:"20px 0 0 0"}}>
                                    <i className="fa fa-bell" aria-hidden="true"></i> Recibir notificación sobre disponibilidad
                                </button>
                            )}
                            <div className="clear"></div>
                            {/* <a rel="noopener noreferrer" href="http://url.com" target="_blank">df</a> */}
                            <div className="whatsapp"><i className="fa fa-whatsapp" aria-hidden="true"></i> Consulta con un Ofertador</div>
                            <div className="twitter"><i className="fa fa-twitter" aria-hidden="true"></i> Tweet</div>
                            <div className="facebook"><i className="fa fa-facebook" aria-hidden="true"></i> Recomended 2014</div>
                        </div>
                    </div>
                    <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div className="details-container">
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: product.descripcion
                                }}>
                            </div>
                            
                            <h2>Opiniones de clientes</h2>
                            {isLoggedIn ? (
                                <>
                                <p>Sé el primero en calificar este producto
                                    <span className="star-rating">
                                    <i className="fa fa-star" aria-hidden="true" onClick={() => this.handleOnRateAndReviewClick(1)}></i>
                                    <i className="fa fa-star" aria-hidden="true" onClick={() => this.handleOnRateAndReviewClick(2)}></i>
                                    <i className="fa fa-star" aria-hidden="true" onClick={() => this.handleOnRateAndReviewClick(3)}></i>
                                    <i className="fa fa-star" aria-hidden="true" onClick={() => this.handleOnRateAndReviewClick(4)}></i>
                                    <i className="fa fa-star" aria-hidden="true" onClick={() => this.handleOnRateAndReviewClick(5)}></i>
                                    </span>
                                    <br/><span style={{color: "red"}}>{this.state.errors["customerRatedNumber"]}</span>
                                </p>
                                    <Form.Group controlId="formGroupRateAndReviewTitle">
                                        <Form.Label>Título *</Form.Label>
                                        <Form.Control type="text" name="ratereviewtitle" value={ratereviewtitle} onChange={this.handleOnRateReviewInputChange} placeholder="Enter Review Title" className={'form-control' + (rateAndReviewSubmitted && !ratereviewtitle ? ' has-error' : '')} maxLength="60"/>
                                        <span style={{color: "red"}}>{this.state.errors["ratereviewtitle"]}</span>
                                    </Form.Group>
                                    <Form.Group controlId="formGroupRateAndReviewDesc">
                                        <Form.Label>Comentario *</Form.Label>
                                        <Form.Control as="textarea" rows="3" name="ratereviewdesc" value={ratereviewdesc} onChange={this.handleOnRateReviewInputChange} className={'form-control' + (rateAndReviewSubmitted && !ratereviewdesc ? ' has-error' : '')} maxLength="260"/>
                                        <span style={{color: "red"}}>{this.state.errors["ratereviewdesc"]}</span>
                                    </Form.Group>
                                <button onClick={this.handleOnRateAndReviewSubmit} >
                                    <i className={`fa fa-refresh fa-spin ${ this.state.ratereviewLoader }`} style={{fontSize:"17px"}}></i> Califica este producto
                                </button>
                                <span style={{color: "green"}}>{this.state.reviewApiSuccessResponse}</span>
                                </>
                            ) : (
                                <>
                                <p>Sé el primero en calificar este producto
                                    <span className="star-rating">
                                    <i className="fa fa-star" aria-hidden="true"></i>
                                    <i className="fa fa-star" aria-hidden="true"></i>
                                    <i className="fa fa-star" aria-hidden="true"></i>
                                    <i className="fa fa-star" aria-hidden="true"></i>
                                    <i className="fa fa-star" aria-hidden="true"></i>
                                    </span>
                                </p>
                                <button onClick={this.openLoginPop}>Califica este producto</button>
                                </>
                            )}    
                        </div>
                    </div>
                </div>
                ))}

                <Modal show={this.state.showNotifyModal} onHide={this.closeNotifyPop} animation={true}>                          
                    <Modal.Body>
                        <div className=""> 
                            <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" className="close" onClick={this.closeNotifyPop}>&times;</button>
                            </div>
                            <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <form className="notify_prod_form" id="forgot-password-form" onSubmit={this.handleOnNotifyProductSubmit}>
                                    <div className="col-xs-12">
                                    <h4 style={{fontSize:"20.5px"}}>Notificar cuando el producto esté disponible</h4>
                                    <p>Suscríbase a este producto para recibir un correo electrónico una vez que esté disponible</p>
                                    </div>
                                    <div className="col-xs-12">
                                        <Form.Group controlId="formGroupCardNumber">
                                            <Form.Control type="email" name="notifytoemail" value={notifytoemail} onChange={this.handleOnNotifyEmailInputChange} placeholder="Enter Your Email" className={'form-control' + (notifyProductSubmitted && !notifytoemail ? ' has-error' : '')} maxLength="60"/>
                                            <span style={{color: "red"}}>{this.state.errors["notifytoemail"]}</span>
                                            <span style={{color: "green"}}>{this.state.notifyApiSuccessResponse}</span>
                                        </Form.Group>
                                    </div>
                                    
                                    <div className="col-xs-12 signup-btn-wrap">
                                        <button className="btn" value="Forgot Password" type="submit">
                                            <i className={`fa fa-refresh fa-spin ${ this.state.notifyProdLoader }`} style={{fontSize:"17px"}}></i> Notificarme
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </Modal.Body>       
                </Modal>

                <Modal show={this.state.showLoginModal} onHide={this.closeLoginPop} animation={true}>                         
                    <Modal.Body>
                        <div className="popupbg"> 
                            <button type="button" className="close" onClick={this.closeLoginPop}>&times;</button>
                            <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12 right-form-area">
                                <div className="right-form-logo mobile-logo-login">
                                    <img src={`${process.env.PUBLIC_URL}/images/popup-logo.png`} title="logo" alt="logo" />
                                </div>
                                <h2> Iniciar sesion</h2>
                                <h3> Obtenga acceso 
                                sus cuentas y
                                pedidos</h3>
                                <div className={`form-wrap ${this.state.apiErrorMessage ? '' : 'hidden'} `}>
                                    <label style={apiError}>{this.state.apiErrorMessage}</label>
                                </div>
                                <div className={`form-wrap ${this.state.apiSuccessMessage ? '' : 'hidden'} `}>
                                    <label style={apiSuccess}>{this.state.apiSuccessMessage}</label>
                                </div>
                                <form className={`login_form ${ this.state.loginFormShowHide }`} id="login-form" onSubmit={this.handleOnProductLoginSubmit}>
                                    <div className="form-wrap">
                                        <div className="input-wrap">
                                        <span className="login-icon">
                                        <img src={`${process.env.PUBLIC_URL}/images/new-images/icon/mail-icon.svg`} alt="Mail Icon" rel="noopener noreferrer" />
                                        </span>
                                        <input className={'input-text' + (loginSubmitted && !username ? ' has-error' : '')} type="email" name="username" value={username} onChange={this.handleOnLoginInputChange} placeholder="Enter Your Email" maxLength="70"/>
                                        </div>
                                        <span style={{color: "red"}}>{this.state.errors["username"]}</span>
                                    </div>
                                    <div className="form-wrap">
                                        <div className="input-wrap">
                                        <span className="login-icon">
                                        <img src={`${process.env.PUBLIC_URL}/images/new-images/icon/password-icon.svg`} alt="Mail Icon" rel="noopener noreferrer" />
                                        </span>
                                        <input className={'input-text' + (loginSubmitted && !password ? ' has-error' : '')} type="password" name="password" value={password} onChange={this.handleOnLoginInputChange} placeholder="&#9679;&#9679;&#9679;&#9679;&#9679;&#9679;&#9679;&#9679;" maxLength="50"/>
                                        </div>
                                        <span style={{color: "red"}}>{this.state.errors["password"]}</span>
                                    </div>
                                    <Link to="#" id="forgots" className="no-account" onClick={e => this.handleOnFormChange("forgotpassword")}>¿Olvidaste tu contraseña? </Link>
                                    <div className="signup-btn-wrap">
                                        <button className="btn" value="Login" id="logins" type="submit" disabled={this.state.loginFormButtonDisabled}>
                                            <i className={`fa fa-refresh fa-spin ${ this.state.loginLoader }`} style={{fontSize:"17px"}}></i> Iniciar sesion
                                        </button>
                                    </div>
                                    <div className="reg-btn-wrap">
                                        <button className="btn" type="button" onClick={e => this.handleOnFormChange("registration")} >Regístrate</button>
                                    </div>
                                </form>

                                <form className={`registration_form ${ this.state.registrationFormShowHide }`} id="registration-form" onSubmit={this.handleOnRegistrationSubmit}>
                                    <div className="form-wrap">
                                        <label>Primer Nombre</label>
                                        <input className={'input-text' + (registrationSubmitted && !firstname ? ' has-error' : '')} name="firstname" value={firstname} onChange={this.handleOnLoginInputChange} placeholder="Enter Your First Name" maxLength="70"/>
                                        <span style={{color: "red"}}>{this.state.errors["firstname"]}</span>
                                    </div>
                                    <div className="form-wrap">
                                        <label>Apellido</label>
                                        <input className={'input-text' + (registrationSubmitted && !lastname ? ' has-error' : '')} name="lastname" value={lastname} onChange={this.handleOnRegistrationInputChange} placeholder="Enter Your Last Name" maxLength="50"/>
                                        <span style={{color: "red"}}>{this.state.errors["lastname"]}</span>
                                    </div>
                                    <div className="form-wrap">
                                        <label>Correo electrónico</label>
                                        <input className={'input-text' + (registrationSubmitted && !email ? ' has-error' : '')} type="email" name="email" value={email} onChange={this.handleOnRegistrationInputChange} placeholder="Enter Your Username" maxLength="50"/>
                                        <span style={{color: "red"}}>{this.state.errors["email"]}</span>
                                    </div>
                                    <div className="form-wrap">
                                        <label>Teléfono</label>
                                        <input className={'input-text' + (registrationSubmitted && !telephone ? ' has-error' : '')} name="telephone" value={telephone} onChange={this.handleOnRegistrationInputChange} placeholder="Enter Your Telephone Number" maxLength="50"/>
                                        <span style={{color: "red"}}>{this.state.errors["telephone"]}</span>
                                    </div>
                                    <div className="form-wrap">
                                        <label>Contraseña</label>
                                        <input className={'input-text' + (registrationSubmitted && !regpassword ? ' has-error' : '')} type="password" name="regpassword" value={regpassword} onChange={this.handleOnRegistrationInputChange} placeholder="Enter Your Password" maxLength="50"/>
                                        <span style={{color: "red"}}>{this.state.errors["regpassword"]}</span>
                                    </div>
                                    <Link to="#" id="forgots" className="no-account" onClick={e => this.handleOnFormChange("login")}>Usuario existente? Iniciar sesion </Link>
                                    <div className="reg-btn-wrap">
                                    <button className="btn" value="Registration" id="reg" type="submit" disabled={this.state.loginFormButtonDisabled}>
                                        <i className={`fa fa-refresh fa-spin ${ this.state.registrationLoader }`} style={{fontSize:"17px"}}></i> Regístrate
                                    </button>
                                    </div>
                                    <div className="signup-btn-wrap">
                                        <button className="btn" type="button" onClick={e => this.handleOnFormChange("login")}>Iniciar sesion</button>
                                    </div>
                                </form>

                                <form className={`forgot_pass_form ${ this.state.forgotPasswordShowHide }`} id="forgot-password-form" onSubmit={this.handleOnForgotPasswordSubmit}>
                                    <div className="form-wrap">
                                        <label>Correo electrónico</label>
                                        <input className={'input-text' + (forgotPasswordSubmitted && !forgotpassemail ? ' has-error' : '')} type="email" name="forgotpassemail" value={forgotpassemail} onChange={this.handleOnRegistrationInputChange} placeholder="Enter Your Email" maxLength="50"/>
                                        <span style={{color: "red"}}>{this.state.errors["forgotpassemail"]}</span>
                                        <span style={{color: "green"}}>{this.state.forgotPasswordApiSuccessResponse}</span>
                                        <span style={{color: "red"}}>{this.state.forgotPasswordApiErrorResponse}</span>
                                    </div>
                                    <Link to="#" id="forgots" className="no-account" onClick={e => this.handleOnFormChange("login")}> Atrás para iniciar sesión? </Link>
                                    <div className="signup-btn-wrap">
                                        <button className="btn" value="Forgot Password" type="submit" disabled={this.state.loginFormButtonDisabled}>
                                            <i className={`fa fa-refresh fa-spin ${ this.state.forgotPassLoader }`} style={{fontSize:"17px"}}></i> Enviar
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </Modal.Body>       
                </Modal>

                <Modal show={this.state.showUpdateCartMsgModal} onHide={this.cartUpdatePopupResClose} animation={true}>                          
                    <Modal.Header closeButton>
                        <Modal.Title>Oops!</Modal.Title>
                    </Modal.Header>
                    <Modal.Body><p style={{textAlign:'center'}}>{this.state.cartErrorMessage}</p></Modal.Body>     
                </Modal>
            </>
        } else {
            return (
                <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div className="details-container">
                        <h2>Loading...</h2>
                    </div>
                </div>
            )
        }
    }
}

function mapStateToProps(state) {
    localStorage.setItem("cartData", JSON.stringify(state.cart.cartData))
    return { 
        isSignedUp: state.user.isSignedUp,
        cartData: state.cart.cartData,
        isLoggedIn: state.user.isLoggedIn,
        isError: state.user.isError,
        errorMessage: state.user.errorMessage,
        isSignupError: state.user.isSignupError,
        signupErrorMessage: state.user.signupErrorMessage,
        isCartAddError: state.cart.isCartAddError,
        cartErrorMessage: state.cart.cartErrorMessage,
        userEmail: (typeof(state.user.user) === 'undefined') ? "" :  state.user.user.email,
        customerId: (typeof(state.user.user) === 'undefined') ? "" :  state.user.user.customer_id
    }
}

const actionCreators = {
    addToCart: addToCart2,
    logIn: login2
}

const ProductDetailsListArea = connect(
    mapStateToProps,
    actionCreators
)(ProductDetailsListAreaComp)

export default withRouter(ProductDetailsListArea);