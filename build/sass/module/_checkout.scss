// Checkout

#progressbar {
  margin-bottom: 30px;
  overflow: hidden;
}

#progressbar .active {
  color: #3c525d;
}

#progressbar strong {
  font-size: 12px;
  font-weight: 400;
}

#progressbar li {
  list-style-type: none;
  font-size: 15px;
  width: 33.3%;
  float: left;
  position: sticky;
  font-weight: 400;
}

//   #progressbar #step1:before {
//     content: "1";
//   }

//   #progressbar #step2:before {
//     content: "2";
//   }

//   #progressbar #step3:before {
//     content: "3";
//   }

//   #progressbar #step4:before {
//     content: "4";
//   }

#progressbar li:before {
  content: "";
  width: 20px;
  height: 20px;
  line-height: 20px;
  display: block;
  border: 4px solid #ffffff;
  background: #ffffff;
  outline: 1px solid #b3d6ea;
  border-radius: 50%;
  margin: 1px auto 10px auto;
  padding: 2px;
}

#progressbar li:after {
  content: "";
  width: 100%;
  height: 2px;
  background: #b3d6ea;
  position: absolute;
  left: 0;
  top: 11px;
  z-index: -1;
}
#progressbar li:first-child:after {
  width: 50%;
  left: inherit;
  right: 0;
}
#progressbar li:last-child:after {
  width: 50%;
}

#progressbar li.active:before {
  background: #0075b9;
  outline-color: #0075b9;
}
#progressbar li.active:after {
  background: #b3d6ea;
}

.progress {
  height: 20px;
}

.progress-bar {
  background-color: #2f8d46;
}

// User info card

.userInfo {
  &.card {
    background-color: #f0faff;
    border: none;
    box-shadow: none;
    border-radius: 16px;
    padding: 35px 5px !important;
    @media (max-width: 767px) {
      padding: 15px 0px !important;
      cursor: default;
    }
  }
  .form-group {
    margin-bottom: 20px;
    @media (max-width: 767px) {
      & {
        margin-bottom: 15px;
      }
    }
    label {
      color: #3c525d;
      font-size: 13px;
      line-height: 27px;
      font-weight: 400;

      @media (max-width: 767px) {
        & {
          margin-bottom: 0;
        }
      }
    }
    .selected-flag {
      margin-left: 0 !important;
    }
    .form-control {
      border-radius: 0;
      background-color: transparent;
      border: none;
      border-bottom: 1px solid #cfd7e5;
      color: #3c525d;
      font-size: 13px;
      line-height: 27px;
      padding: 5px 0;
      height: 40px;
    }
    .bol-label {
      color: #3c525d;
      font-weight: 600;
      font-size: 18px;
      line-height: 30px;
    }
    .form-check {
      position: relative;
      padding-left: 0;
      .form-check-label {
        display: flex;
        align-items: center;
        input[type="checkbox"] {
          position: relative;
          margin: 0;
          width: 16px;
          height: 16px;
        }
        input[type="checkbox"]:checked + label::before {
          content: "";
          display: block;
          position: absolute;
          text-align: center;
          height: 20px;
          width: 20px;
          left: 0;
          top: 5px;
          background-color: #fa9e57;
          font-family: "Montserrat";
          border-radius: 2px;
          border: 1px solid rgba(150, 150, 150, 0.3);
        }

        input[type="checkbox"]:checked + label::after {
          content: url('data:image/svg+xml; utf8, <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="white" viewBox="0 0 24 24"><path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z"/></svg>');
          display: block;
          position: absolute;
          left: 3px;
          top: 3px;
        }
      }
    }
  }
}
.checkout-table-block {
  background-color: #f0faff;
  border-radius: 16px;
  padding: 35px;
  display: inline-block;
  width: 100% !important;
  position: relative;
  z-index: 9;
  @media (max-width: 767px) {
    padding: 17px;
  }
  .title-table {
    font-weight: 600;
    font-size: 24px;
    line-height: 28px;
    text-transform: capitalize;
    color: #3c525d;
    display: flex;
    align-items: center;
  }
  th {
    font-size: 14px;
    font-weight: 600;
    color: #3c525d;
    border-bottom: 1px solid #cfd7e5 !important;
    padding: 10px 8px;
    &:first-child {
      padding-left: 0;
    }
  }
  tr {
    &.last-row {
      border-top: 1px solid #cfd7e5;
      td {
        font-size: 14px;
        strong {
          font-weight: 600;
        }
      }
    }
    &.blank-tr {
      td {
        padding: 2px;
      }
    }
  }
  td {
    font-size: 12px;
    color: #3c525d;
    border-top: none !important;
    &:first-child {
      padding-left: 0;
    }
  }
  .table-button {
    width: 100%;
    display: inline-block;
    margin-top: 10px;
    .btn {
      background: #0075b9 !important;
      border-color: #0075b9 !important;
      width: 100%;
      padding: 12px;
      border-radius: 50px;
      font-size: 16px !important;
      img {
        width: 11px;
        margin-left: 10px;
      }
    }
  }
}
.block-title-2 {
  font-weight: 600;
  font-size: 24px;
  line-height: 28px;
  text-transform: capitalize;
  color: #3c525d;
  display: flex;
  align-items: center;
  @media (max-width: 1024px) {
    font-size: 20px;
  }
  @media (max-width: 767px) {
    font-size: 18px;
    line-height: 24px;
  }
  .bg-secondary {
    background-color: #0075b9 !important;
    font-size: 20px;
    font-weight: 600;
    padding: 6px 8px;
    border-radius: 5px;
    margin-right: 5px;
    @media (max-width: 767px) {
      font-size: 16px;
      padding: 6px 10px 6px 8px;
    }
  }
}

.general-card-wrapper {
  position: relative;
  background-color: #f0faff;
  border-radius: 16px;
  padding: 35px;
  @media (max-width: 767px) {
    padding: 17px;
  }
}

// Payment section style

.paymentBox {
  position: relative;
  background-color: #f0faff;
  border-radius: 16px;
  padding: 35px;
  @media (max-width: 767px) {
    padding: 17px;
  }
  .payment-card {
    position: relative;
    border: none;
    box-shadow: none;
    background-color: transparent;
    .card-header {
      background-color: transparent;
      border: none;
    }
    .card-body {
      border: 1px solid #ddf0f9;
      border-radius: 10px;
      background-color: #fff;
      @media (max-width: 767px) {
        padding: 15px;
        margin-bottom: 15px;
      }
      p {
        font-size: 12px;
        color: #3c525d;
        line-height: 21px;
        font-weight: 500;
        &.short-p {
          font-size: 10px;
          line-height: 18px;
          font-weight: 400;
          a {
            color: #0075b9;
          }
        }
      }
    }
  }
}

.couponblock {
  margin-top: 20px;
  .form-group {
    margin: 0;
  }
  label {
    font-size: 20px;
    line-height: 27px;
    font-weight: 600;
    color: #3c525d;
    margin: 0 0 18px;
  }
  .form-control {
    border-radius: 0;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #cfd7e5;
    color: #3c525d;
    font-size: 13px;
    line-height: 27px;
    padding: 5px 0;
    height: 40px;
    option {
      color: #3c525d;
    }
    &::placeholder {
      color: #3c525d;
      opacity: 0.9;
    }

    &::-ms-input-placeholder {
      color: #3c525d;
      opacity: 0.9;
    }
  }

  .btn {
    background: #0075b9 !important;
    border-color: #0075b9 !important;
    padding: 10px 15px;
    border-radius: 50px;
    font-size: 16px !important;
    color: #fff;
    font-weight: 500;
    margin-top: 25px;
    img {
      width: 11px;
      margin-left: 10px;
    }
  }
}

.toggle_btn {
  width: 28px;
  height: 28px;
  background: none;
  position: relative;
  padding: 0;
  margin: 0 10px 0 0;
  @media (max-width: 767px) {
    width: 20px;
    height: 20px;
    margin: 0 7px 0 0;
  }
  &:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: transparent;
    border: 1.2px solid #a8cee0;
    border-radius: 50%;
    top: 0;
    left: 0;
  }
}
.payment_radio_btn {
  width: 28px;
  height: 28px;
  position: relative;
  margin: 0 !important;
  opacity: 0;
  mix-blend-mode: multiply;
  @media (max-width: 767px) {
    width: 20px;
    height: 20px;
  }
  &:checked {
    opacity: 1;
    accent-color: #0075b9;
    mix-blend-mode: multiply;
  }
}
.payment_title_txt {
  position: relative;
  color: #3c525d;
  top: -7px;
  font-size: 16px;
  font-weight: 600 !important;
  @media (max-width: 992px) {
    font-size: 15px;
  }
  @media (max-width: 767px) {
    font-size: 14px;
    top: 0;
  }
}
.card_img {
  width: 67px;
  float: right;
}
.yappy_img {
  width: 40px;
  float: right;
}
.bnktrns_img {
  width: 37px;
  float: right;
}
.term-condition-line {
  position: relative;
  font-weight: 400 !important;
  font-size: 13px !important;
  @media (max-width: 992px) {
    flex-wrap: wrap;
  }
  input[type="checkbox"] {
    margin: 0 5px 0 0;
  }
  a {
    color: #0075b9;
    margin: 0 5px;
  }
}
.blog-buttton {
  position: relative;
  margin-top: 10px;
  display: inline-block;
  width: 100%;
  @media (max-width: 767px) {
    display: block;
  }
  .btn {
    background-color: #0075b9 !important;
    border-color: #0075b9 !important;
    padding: 12px;
    border-radius: 50px;
    color: #fff !important;
    width: 100%;
    max-width: 237px;
    font-size: 16px;
    font-weight: 500;

    @media (max-width: 767px) {
      max-width: inherit;
    }
    img {
      width: 11px;
      margin-left: 10px;
    }
  }
}
.payment-small-tittle {
  font-size: 18px;
  line-height: 21px;
  color: #3c525d;
  font-weight: 500;
  margin-bottom: 10px;
}

.same-checked {
  input {
    margin: 0 10px 0 0;
    top: inherit !important;
  }
  label {
    margin: 0;
    line-height: 16px !important;
  }
}
