/*=========================================
1. Template Default Styles                                 
==========================================*/
/*@import url(https://db.onlinewebfonts.com/c/1909d4a43c13e5f37766baf5a4e14607?family=dashicons);*/
html,
body {
  height: 100%;
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  vertical-align: baseline;
  background: #ffffff;
  color: #707070;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #222222;
  line-height: 1.4;
  font-weight: 500;
  font-family: "Poppins", sans-serif;
  margin: 0 0 10px 0;
}
h1 {
  font-size: 72px;
  font-family: "Poppins", sans-serif;
  text-transform: uppercase;
}
h2 {
  font-size: 48px;
}
h3 {
  font-size: 24px;
  color: #606060;
}
h4 {
  font-size: 20px;
}
p {
  line-height: 20px;
  margin: 0 0 10px 0;
}

a {
  color: #333333;
  text-decoration: none;
  background-color: transparent;
  text-decoration: none;
}

a {
  text-decoration: none;
}
a:active,
a:hover,
a:focus {
  text-decoration: none;
}
a:active,
a:hover,
a:focus {
  outline: 0 none;
}
img {
  max-width: 100%;
  height: auto;
}
ul {
  list-style: outside none none;
  margin: 0;
  padding: 0;
}
.clear:after {
  clear: both;
  content: "";
  display: block;
}
.wraper {
  overflow: hidden;
}
.form-control {
  border: 1px solid #dddddd;
  box-shadow: none;
}
.form-control::-moz-placeholder {
  color: #646464;
}
.padding-top-bottom {
  padding: 40px 0;
}
.padding-two-top-bottom {
  padding: 40px 0 40px 0;
}
.padding-top {
  padding-top: 40px;
}
.padding-bottom {
  padding-bottom: 40px;
}
.default-button-btn {
  border-radius: 3px;
  color: #ffffff;
  background: #000;
  display: block;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 600;
  text-transform: capitalize;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}
.default-button-btn:hover {
  background: #f76a00;
  color: #ffffff;
}
.ghost-btn {
  border: 2px solid #009d60;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 600;
  padding: 10px 20px;
  background: transparent;
  display: inline-block;
  text-transform: capitalize;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}
.ghost-btn:hover {
  background: #19abbd;
  color: #ffffff;
}
.title-section {
  margin-bottom: 20px;
  text-align: center;
}
.title-section h2 {
  font-size: 36px;
  margin-bottom: 10px;
}

button:focus {
  outline: none !important;
  outline: none !important;
}
button {
  border: 3px !important;
}

/* SLIDER CSS */

.bs-slider {
  overflow: hidden;
  max-height: 440px;
  position: relative;
  background: #fff;
}
.bs-slider:hover {
  cursor: -moz-grab;
  cursor: -webkit-grab;
}
.bs-slider:active {
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
}
.bs-slider .bs-slider-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}
.bs-slider > .carousel-inner > .item > img,
.bs-slider > .carousel-inner > .item > a > img {
  margin: auto;
  width: 100% !important;
  height: auto !important;
}

/*---------- LEFT/RIGHT ROUND CONTROL ----------*/
.control-round .carousel-control {
  top: 47%;
  opacity: 0;
  width: 45px;
  height: 45px;
  z-index: 100;
  color: #ffffff;
  display: block;
  font-size: 24px;
  cursor: pointer;
  overflow: hidden;
  line-height: 43px;
  text-shadow: none;
  position: absolute;
  font-weight: normal;
  background: transparent;
  -webkit-border-radius: 100px;
  border-radius: 100px;
}
.control-round:hover .carousel-control {
  opacity: 1;
}
.control-round .carousel-control.left {
  left: 1%;
}
.control-round .carousel-control.right {
  right: 1%;
}
.control-round .carousel-control.left:hover,
.control-round .carousel-control.right:hover {
  color: #fdfdfd;
  background: rgba(0, 0, 0, 0.5);
  border: 0px transparent;
}
.control-round .carousel-control.left > span:nth-child(1) {
  left: 45%;
}
.control-round .carousel-control.right > span:nth-child(1) {
  right: 45%;
}

.carousel .slide {
  height: 100%;
}

/*---------- INDICATORS CONTROL ----------*/
.indicators-line > .carousel-indicators {
  right: 45%;
  bottom: 3%;
  left: auto;
  width: 90%;
  height: 20px;
  font-size: 0;
  overflow-x: auto;
  text-align: right;
  overflow-y: hidden;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 1px;
  white-space: nowrap;
}
.indicators-line > .carousel-indicators li {
  padding: 0;
  width: 15px;
  height: 15px;
  border: 1px solid rgb(158, 158, 158);
  text-indent: 0;
  overflow: hidden;
  text-align: left;
  position: relative;
  letter-spacing: 1px;
  background: rgb(158, 158, 158);
  -webkit-font-smoothing: antialiased;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  margin-right: 5px;
  -webkit-transition: all 0.5s cubic-bezier(0.22, 0.81, 0.01, 0.99);
  transition: all 0.5s cubic-bezier(0.22, 0.81, 0.01, 0.99);
  z-index: 10;
  cursor: pointer;
}
.indicators-line > .carousel-indicators li:last-child {
  margin-right: 0;
}
.indicators-line > .carousel-indicators .active {
  margin: 1px 5px 1px 1px;
  box-shadow: 0 0 0 2px #fff;
  background-color: transparent;
  position: relative;
  -webkit-transition: box-shadow 0.3s ease;
  -moz-transition: box-shadow 0.3s ease;
  -o-transition: box-shadow 0.3s ease;
  transition: box-shadow 0.3s ease;
  -webkit-transition: background-color 0.3s ease;
  -moz-transition: background-color 0.3s ease;
  -o-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}
.indicators-line > .carousel-indicators .active:before {
  transform: scale(0.5);
  background-color: #fff;
  content: "";
  position: absolute;
  left: -1px;
  top: -1px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  -webkit-transition: background-color 0.3s ease;
  -moz-transition: background-color 0.3s ease;
  -o-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

/*---------- SLIDE CAPTION ----------*/
.slide_style_left {
  text-align: left !important;
}
.slide_style_right {
  text-align: right !important;
}
.slide_style_center {
  text-align: center !important;
}

#bootstrap-touch-slider .item {
  min-height: 350px;
}

.slide-text {
  right: 0;
  top: 10%;
  right: 0;
  margin: 0;
  padding: 10px;
  position: absolute;
  text-align: left;
  padding: 10px 85px 10px 10px;
  width: 50%;
}

.slide-image-cell {
  left: 0;
  top: 0%;
  right: 0;
  margin: 0;
  padding: 10px;
  position: absolute;
  text-align: left;
  padding: 10px 10px 10px 85px;
  width: 50%;
}

.slide-text > h1 {
  padding: 0;
  color: #348ee6;
  font-size: 70px;
  font-style: normal;
  font-weight: 900;
  line-height: 84px;
  margin-bottom: 0px;
  letter-spacing: 1px;
  display: inline-block;
  -webkit-animation-delay: 0.7s;
  animation-delay: 0.7s;
}

.slide-text > h1 span {
  float: left;
  width: 100%;
  font-size: 48px;
  line-height: 48px;
}

.slide-text > p {
  padding: 0;
  color: #333;
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  letter-spacing: 1px;
  -webkit-animation-delay: 1.1s;
  animation-delay: 1.1s;
}
.slide-text > a.btn-default {
  color: #000;
  font-weight: 400;
  font-size: 13px;
  line-height: 15px;
  margin-right: 0px;
  text-align: center;
  padding: 10px 30px;
  white-space: nowrap;
  letter-spacing: 1px;
  display: inline-block;
  border: none;
  text-transform: uppercase;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
  -webkit-transition: background 0.3s ease-in-out, color 0.3s ease-in-out;
  transition: background 0.3s ease-in-out, color 0.3s ease-in-out;
}
.slide-text > a.btn-primary {
  color: #ffffff;
  cursor: pointer;
  font-weight: 400;
  font-size: 13px;
  line-height: 15px;
  margin-left: 10px;
  text-align: center;
  padding: 10px 30px;
  white-space: nowrap;
  letter-spacing: 1px;
  background: #00bfff;
  display: inline-block;
  text-decoration: none;
  text-transform: uppercase;
  border: none;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
  -webkit-transition: background 0.3s ease-in-out, color 0.3s ease-in-out;
  transition: background 0.3s ease-in-out, color 0.3s ease-in-out;
}
.slide-text > a:hover,
.slide-text > a:active {
  color: #ffffff;
  background: #222222;
  -webkit-transition: background 0.5s ease-in-out, color 0.5s ease-in-out;
  transition: background 0.5s ease-in-out, color 0.5s ease-in-out;
}

.whatsapp-img {
  width: 25px !important;
}

/*------------------------------------------------------*/
/* RESPONSIVE
/*------------------------------------------------------*/

@media (max-width: 991px) {
  .slide-text h1 {
    font-size: 30px;
    line-height: 34px;
    margin-bottom: 20px;
  }
  .slide-text > h1 span {
    float: left;
    width: 100%;
    font-size: 22px;
    line-height: 30px;
  }
  .slide-text > p {
    font-size: 18px;
  }
}

/*---------- MEDIA 480px ----------*/
@media (max-width: 768px) {
  .brandshop-container .bs img {
    /* padding: 3px 0px;
    width: 80px; */
  }
  .col-md-3-brand {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .section-title-inner {
    font-size: 1.3rem;
  }

  .slide-text {
    padding: 10px 50px;
  }
  .slide-text h1 {
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 10px;
  }
  .slide-text > h1 span {
    float: left;
    width: 100%;
    font-size: 22px;
    line-height: 30px;
  }
  .slide-text > p {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 20px;
  }
  .control-round .carousel-control {
    display: none;
  }
}
@media (max-width: 480px) {
  .product-banner-container {
    display: none !important;
  }
  /* .product-breadcamp-container {
    background: #498eed !important;
    margin-bottom: 0px !important;
    padding: 10px;
    background: #498eed !important;
    color: #fff !important;
  } */
  /* .product-breadcamp-container a {
    color: #fff !important;
  }
  .product-breadcamp-container span {
    font-weight: 700;
  } */
  .listing-container {
    padding: 5px 0 !important;
  }
  .slide-text {
    padding: 10px 30px;
  }
  .slide-text h1 {
    font-size: 20px;
    line-height: 25px;
    margin-bottom: 5px;
  }
  .slide-text > h1 span {
    float: left;
    width: 100%;
    font-size: 22px;
    line-height: 30px;
  }
  .slide-text > p {
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 10px;
  }
  .slide-text > a.btn-default,
  .slide-text > a.btn-primary {
    font-size: 10px;
    line-height: 10px;
    margin-right: 0px;
    margin-left: 0px;
    text-align: center;
    padding: 10px 15px;
  }
  .indicators-line > .carousel-indicators {
    display: none;
  }
}

.slider-container {
  min-height: 205px;
  margin-top: 0px;
}

.slider-container .left_banner_section {
  float: left;
  width: 63%;
  overflow: hidden;
  margin-right: 0px;
  margin-top: 5px;
  height: 205px;
}

.left_banner_section .slick-slide img {
  width: 100%;
  max-height: 205px !important;
  height: auto;
  min-height: 205px;
  max-width: 100% !important;
}

.slider-container .right_banner_section {
  float: right;
  width: 37%;
  height: 205px;
  margin-top: 5px;
  overflow: hidden;
}

.right_banner_section .slick-slide img {
  width: 100%;
  max-height: 205px !important;
  height: 205px;
  min-height: 205px;
  max-width: 100% !important;
  text-align: center;
}

.right_banner_section .slick-slider .item {
  text-align: center;
}

.slider-container .right_banner_section .slick-prev {
  left: 5px;
  display: block !important;
}

.slider-container .right_banner_section .slick-next {
  right: 2px;
  display: block !important;
}

.right_banner_section .slick-next:before,
.right_banner_section .slick-prev:before {
  color: #000000 !important;
}

._3vt7_Mh4hRCFbp__dFqBCI {
  /* position: relative;
  text-align: left;
  width: 125px !important;
  float: left; */
}
._2iA8p44d0WZ-WqRBGcAuEV {
  /* border: none !important;
  border-radius: 0px !important;
  padding: 0 !important;
  min-height: auto !important;
  position: relative; */
}
._3vt7_Mh4hRCFbp__dFqBCI input {
  /* margin-top: 0px !important; */
}
.searchBox {
  /* width: 96% !important; */
}

._3vLmCG3bB3CM2hhAiQX1tN {
  /* position: absolute;
  width: 200px !important;
  background: #fff !important;
  border-radius: 4px;
  margin-top: 0px !important;
  z-index: 2; */
}

._3vt7_Mh4hRCFbp__dFqBCI input[type="checkbox"] {
  /* margin-top: 8px !important;
  float: left !important;
  margin-right: 10px !important; */
}

/* Slider */

.slick-slide {
  margin: 0px 20px;
}

.slick-slide img {
  max-width: 100%;
  height: 100%;
}

.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}
.slick-list:focus {
  outline: none;
}
.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
}
.slick-track:before,
.slick-track:after {
  display: table;
  content: "";
}
.slick-track:after {
  clear: both;
}
.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}
[dir="rtl"] .slick-slide {
  float: right;
}
.slick-slide img {
  width: 100%;
  max-height: 450px;
}
.partner-container .slick-slide img {
  max-width: 62%;
  height: auto;
  @media (min-width: 768px) {
    filter: grayscale(100%);
  }
}
.partner-container .slick-slide img:hover {
  filter: grayscale(0);
}
address,
dl,
ol,
ul {
  /* margin-bottom: 0 !important; */
}
.navbar {
  position: relative;
  padding: 0 !important;
}

.slick-slide.slick-loading img {
  display: none;
}
.slick-slide.dragging img {
  pointer-events: none;
}
.slick-initialized .slick-slide {
  display: block;
}
.slick-loading .slick-slide {
  visibility: hidden;
}
.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}
.slick-arrow.slick-hidden {
  display: none;
}

.thumbnail-slider-wrap {
  margin-top: 15px;
  height: 85px;
}
.thumbnail-slider-wrap .slick-track .slick-slide {
  text-align: center;
}
.thumbnail-slider-wrap .slick-track .slick-slide img {
  width: 70%;
}
/* =================== */

/* partner logo Section */
.partner-container {
  background: #fff;
  padding: 30px 0;
}

.slick1-slide {
  margin: 0px 20px;
}

.slick1-slide img {
  max-width: 100%;
  height: 100%;
}

.slick1-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick1-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}
.slick1-list:focus {
  outline: none;
}
.slick1-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick1-slider .slick1-track,
.slick1-slider .slick1-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick1-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
}
.slick1-track:before,
.slick1-track:after {
  display: table;
  content: "";
}
.slick1-track:after {
  clear: both;
}
.slick1-loading .slick1-track {
  visibility: hidden;
}

.slick1-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}
[dir="rtl"] .slick1-slide {
  float: right;
}
.slick1-slide img {
  max-width: 100%;
  height: auto;
}
.slick1-slide.slick-loading img {
  display: none;
}
.slick1-slide.dragging img {
  pointer-events: none;
}
.slick1-initialized .slick1-slide {
  display: block;
  text-align: center;
}
.slick1-loading .slick1-slide {
  visibility: hidden;
}
.slick1-vertical .slick1-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}
.slick1-arrow.slick1-hidden {
  display: none;
}

.slick-prev {
  display: inline-block;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50%;
  position: absolute;
  overflow: hidden;
  background: none !important;
  border: 0px solid #ccc !important;
  top: 40%;
  z-index: 999999;
  left: -6px;
}

.slick-next {
  display: inline-block;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50%;
  position: absolute;
  overflow: hidden;
  background: none !important;
  border: 0px solid #ccc !important;
  top: 40%;
  z-index: 999999;
  right: -6px;
}

.slick-next:before,
.slick-prev:before {
  color: #fff !important;
  font-size: 38px !important;
  line-height: 54px !important;
}

.slick-prev .fa {
  font-size: 30px;
}
.slick-next .fa {
  font-size: 30px;
}

.customer-logos1 .slick1-prev {
  opacity: 0 !important;
}
.customer-logos1 .slick1-next {
  opacity: 0 !important;
}

.upsells-container .upsell-product-item {
  float: left;
}
.upsells-container .upsell-product-item figure {
  margin: 0 !important;
  width: 40%;
  float: left;
  padding: 10px 30px 10px 15px;
}
.upsells-container .upsell-product-item .heading-wrap {
  margin: 0 !important;
  width: 59%;
  float: left;
}
.upsells-container .upsell-product-item .heading-wrap .price {
  margin-top: 55px !important;
  width: 30%;
  float: right;
  font-size: 18px !important;
  font-weight: bold !important;
  text-align: right;
  color: #f44336;
}
.upsells-container .upsell-product-item .heading-wrap h2 {
  margin-top: 20px !important;
  width: 70%;
  float: left;
  font-size: 17px !important;
  line-height: 25px !important;
}
.upsells-container .slick-initialized .slide {
  width: 100% !important;
  float: left;
  border: 0px solid #ccc;
}
.upsells-container .slick-initialized .slide button {
  border-radius: 2px;
  width: 55% !important;
  background-color: #5c8cf8 !important;
  color: #fff;
  float: left;
  font-size: 15px !important;
  margin-right: 15px;
  padding: 5px;
}
.upsells-container .slick-initialized .slide button:hover {
  background-color: #ff0101 !important;
  color: #fff;
}
.upsells-container .heading-wrap button {
  border-radius: 2px;
  width: 55% !important;
  background-color: #5c8cf8 !important;
  color: #fff;
  float: left;
  font-size: 15px !important;
  margin-right: 15px;
  padding: 5px;
}
.upsells-container .heading-wrap button:hover {
  background-color: #ff0101 !important;
  color: #fff;
}
.upsells-container .slick-initialized .slide .upprodlink {
  border-radius: 2px;
  width: 55% !important;
  background-color: #8bc34a !important;
  color: #fff;
  float: left;
  font-size: 15px !important;
  margin-right: 15px;
  padding: 5px 5px;
  line-height: 1.3;
}
.upsells-container .slick-next:before,
.upsells-container .slick-prev:before {
  color: #292929 !important;
  font-size: 23px !important;
  line-height: 50px !important;
}
.upsells-container .slick-next,
.upsells-container .slick-prev {
  display: block !important;
}

.quote-modal-heading {
  font-size: 17.5px;
  padding: 3px 30px 10px 30px;
  color: #047822;
  text-align: center;
}

/* =================== */
/* listing page css */

/* filter form css */

/* Button used to open the contact form - fixed at the bottom of the page */
.open-button {
  /*   background-color: #555;
  color: white;
  padding: 16px 20px;
  border: none;
  cursor: pointer;
  opacity: 0.8;
  position: fixed;
  bottom: 23px;
  right: 28px;
  width: 280px; */
  float: right;
}
.open-button img {
  margin: 0px !important;
}

/* Add styles to the form container */
.form-container {
  max-width: 300px;
  padding: 10px;
  background-color: white;
}

.form-popup .closebtn {
  position: absolute;
  top: 0;
  right: 9px;
  font-size: 23px;
  margin-left: 50px;
  color: #333;
}

#myFormdiv {
  position: fixed;
  background: #000;
  height: 100%;
  width: 100%;
  opacity: 0.8;
  top: 0;
  left: 0;
  z-index: 99999999;
}

.feedback-btn {
  position: fixed;
  right: 0;
  top: 50%;
  cursor: pointer;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background-color: #f4606e;
  color: #fff;
  -webkit-transform-origin: right bottom;
  -ms-transform-origin: right bottom;
  transform-origin: right bottom;
  -webkit-transform: translateY(-100%) rotate(270deg) translateX(50%);
  -ms-transform: translateY(-100%) rotate(270deg) translateX(50%);
  transform: translateY(-100%) rotate(270deg) translateX(50%);
  padding: 5px 10px 5px 10px;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.6em;
  z-index: 9999999 !important;
}

.feedback-btn:hover {
  background-color: #f64556;
}
#feedback-form {
  justify-content: center;
  align-items: center;
}
.feedback-stars {
  position: relative;
  float: left;
  margin-top: 10px;
  width: 45%;
  left: 120px;
}
.feedback-stars input {
  display: none;
}
.feedback-stars input:checked ~ label:not(.reset) {
  -webkit-animation: wobble 0.8s ease-out;
  animation: wobble 0.8s ease-out;
  color: #fbc416;
}
.feedback-stars label:not(.reset) {
  display: inline-block;
  position: relative;
  height: 40px;
  font-size: 30px;
  cursor: pointer;
  color: #3d3d3d;
  transition: color 0.1s ease-out;
  z-index: 10;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  float: right;
  padding: 0px 5px;
}
.feedback-stars input:hover ~ label:not(.reset) {
  -webkit-animation: wobble 0.8s ease-out;
  animation: wobble 0.8s ease-out;
  color: #fbc416;
}
.feedback-face {
  position: relative;
  width: 135px;
  background: white;
  border: 6px solid #f0f0f0;
  border-radius: 100%;
  transition: box-shadow 0.4s ease-out;
  margin-top: 40px;
  margin-left: 10px;
}
.feedback-face:after {
  content: "";
  display: block;
  padding-bottom: 100%;
}
.feedback-face i {
  position: absolute;
  top: 42%;
  display: block;
  width: 10px;
  height: 14px;
  border-radius: 100%;
  background: #f0f0f0;
}
.feedback-face i:nth-child(1) {
  left: 25%;
}
.feedback-face i:nth-child(2) {
  right: 25%;
}
.feedback-face u {
  position: absolute;
  right: 0;
  bottom: 22%;
  left: 0;
  margin: auto;
  width: 30px;
  height: 30px;
  text-decoration: none;
  border: 6px solid #f0f0f0;
  border-radius: 100%;
}
.feedback-face u:before,
.feedback-face u:after {
  content: "";
  position: absolute;
  /*top: 15px;
  width: 6px;
  height: 6px;*/
  background: #f0f0f0;
  border-radius: 60px 60px 0 0;
  z-index: 2;
}
.feedback-face u:before {
  left: -5px;
  -webkit-transform: rotate(-32deg);
  transform: rotate(-32deg);
}
.feedback-face u:after {
  right: -5px;
  -webkit-transform: rotate(32deg);
  transform: rotate(32deg);
}
.feedback-face u .feedback-cover {
  position: absolute;
  top: 5px;
  left: -8px;
  width: 40px;
  height: 100%;
  border: 6px solid white;
  background: white;
  -webkit-transform: translate(0, -12px);
  transform: translate(0, -12px);
}

.feedback-stars input#star4:checked ~ .feedback-face u,
.feedback-stars input#star2:checked ~ .feedback-face u {
  width: 39px;
  bottom: 20%;
}
.feedback-stars input#star4:checked ~ .feedback-face u:before,
.feedback-stars input#star4:checked ~ .feedback-face u:after,
.feedback-stars input#star2:checked ~ .feedback-face u:before,
.feedback-stars input#star2:checked ~ .feedback-face u:after {
  top: 18px;
  height: 10px;
}
.feedback-stars input#star4:checked ~ .feedback-face u:before,
.feedback-stars input#star2:checked ~ .feedback-face u:before {
  left: 0px;
  -webkit-transform: rotate(-66deg);
  transform: rotate(-66deg);
}
.feedback-stars input#star4:checked ~ .feedback-face u:after,
.feedback-stars input#star2:checked ~ .feedback-face u:after {
  right: 0px;
  -webkit-transform: rotate(66deg);
  transform: rotate(66deg);
}
.feedback-stars input#star4:checked ~ .feedback-face u .feedback-cover,
.feedback-stars input#star2:checked ~ .feedback-face u .feedback-cover {
  left: -6px;
  -webkit-transform: translate(0, -12px);
  transform: translate(0, -12px);
}

.feedback-stars input#star5:checked ~ .feedback-face u,
.feedback-stars input#star4:checked ~ .feedback-face u {
  -webkit-transform: rotate(180deg) translateY(-20px);
  transform: rotate(180deg) translateY(-20px);
}

.feedback-stars input#star3:checked ~ .feedback-face u {
  width: 42px;
  height: 6px;
  background: #3d3d3d;
  border: none;
  border-radius: 60px;
  -webkit-transform: translateY(-1px);
  transform: translateY(-1px);
}
.feedback-stars input#star3:checked ~ .feedback-face u:before,
.feedback-stars input#star3:checked ~ .feedback-face u:after,
.feedback-stars input#star3:checked ~ .feedback-face u .feedback-cover {
  display: none;
}

.feedback-stars input:not(#star-reset):checked ~ .feedback-face {
  -webkit-animation: wobble 0.8s ease-out;
  animation: wobble 0.8s ease-out;
}
.feedback-stars input:not(#star-reset):checked ~ .feedback-face,
.feedback-stars input:not(#star-reset):checked ~ .feedback-face u {
  border-color: #d2cfcf;
}
.feedback-stars input:not(#star-reset):checked ~ .feedback-face i,
.feedback-stars input:not(#star-reset):checked ~ .feedback-face u:before,
.feedback-stars input:not(#star-reset):checked ~ .feedback-face u:after {
  background: #3d3d3d;
}

.feedback-stars input#star5:checked ~ .feedback-face {
  background-color: #fa5563;
}
.feedback-stars input#star1:checked ~ .feedback-face u {
  background: #3d3d3d;
  width: 45px;
  bottom: 18%;
}
.feedback-stars input#star5:checked ~ .feedback-face u .feedback-cover {
  background: #fa5563;
  border-color: #fa5563;
}

.feedback-stars input#star4:checked ~ .feedback-face {
  background-color: #fa824e;
}
.feedback-stars input#star4:checked ~ .feedback-face u .feedback-cover {
  background: #fa824e;
  border-color: #fa824e;
}

.feedback-stars input#star3:checked ~ .feedback-face {
  background-color: #fccd3f;
}

.feedback-stars input#star2:checked ~ .feedback-face {
  background-color: #a0d77a;
}
.feedback-stars input#star2:checked ~ .feedback-face u .feedback-cover {
  background: #a0d77a;
  border-color: #a0d77a;
}

.feedback-stars input#star1:checked ~ .feedback-face {
  background-color: #6bca6c;
}
.feedback-stars input#star1:checked ~ .feedback-face u .feedback-cover {
  background: #6bca6c;
  border-color: #6bca6c;
  top: 4px;
  left: -15px;
  width: 55px;
}
@-webkit-keyframes wobble {
  0% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
  }
  20% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
  40% {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }
  60% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  80% {
    -webkit-transform: scale(0.96);
    transform: scale(0.96);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes wobble {
  0% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
  }
  20% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
  40% {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }
  60% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  80% {
    -webkit-transform: scale(0.96);
    transform: scale(0.96);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.feedbackrate-error {
  color: red;
  float: left;
  text-align: center;
  width: 100%;
}
/* dashboard css added */

.loader-container {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  top: 0;
  left: 0;
  z-index: 999999;
}

.loader {
  border: 6px solid #cccccc;
  border-radius: 50%;
  border-top: 6px solid #3498db;
  width: 60px;
  height: 60px;
  -webkit-animation: spin 1s linear infinite; /* Safari */
  animation: spin 1s linear infinite;
  margin: 0px auto;
  z-index: 99999999;
  margin-top: 25%;
}

.fa-user-secret {
  font-size: 20px;
  position: relative;
  top: 2px;
  color: #939393 !important;
  padding-right: 3px !important;
}

.fa-sign-out {
  font-size: 20px;
  position: relative;
  top: 2px;
  color: #a9a8a8 !important;
  padding-right: 3px !important;
}

.g-heading {
  text-align: center;
  font-size: 19px;
  margin-top: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.g-text {
  font-size: 11px;
  text-align: center;
}

.g-paragraph {
  text-align: center;
  margin-top: 5px;
}

.variation_error_msg {
  color: #fe2c2c !important;
  border: 1px solid #fa8181;
  font-size: 15px;
  background: #ffc9c9bf;
  padding: 4px 13px;
  text-transform: none;
}

.attrColorDiv input[type="radio"] {
  display: none;
  position: absolute;
}
.attrSizeDiv input[type="radio"] {
  display: none;
  position: absolute;
}

.attrsizeradio {
  float: left;
}

.attributeSection {
  border: 1px solid #e3e3e3;
  padding: 2px 5px 15px 17px;
  float: left;
  margin-top: 20px;
}
.attrSizeDiv {
  float: left;
  width: 100%;
  margin-top: 20px;
}
.attrSizeDiv h4 {
  font-size: 14px;
  font-weight: 600;
}
.attrSizeDiv .selected {
  border: 1px solid #17a5d1;
  background-color: #33b8f80f;
}
.sizeAttributes {
  float: left;
  padding: 3px 13px;
  border: 1px solid #ccc;
  margin-right: 10px;
  cursor: pointer;
}
.sizeAttributes:hover {
  border: 1px solid #17a5d1;
  background-color: #33b8f80f;
}
.attrSizeDiv input[type="radio"]:checked ~ label {
  border: 1px solid #17a5d1;
  background-color: #33b8f80f;
}
.attrColorDiv {
  float: left;
  width: 100%;
  margin-top: 20px;
}
.attrColorDiv h4 {
  font-size: 14px;
  font-weight: 600;
}
.attrColorDiv .selected {
  border: 1px solid #f6986c;
  background-color: #f881330d;
}
.attrColorDiv input[type="radio"]:checked ~ label {
  border: 1px solid #f6986c;
  background-color: #f881330d;
}
.colorAttributes {
  float: left;
  padding: 2px;
  border: 1px solid #ccc;
  margin-right: 10px;
  cursor: pointer;
  width: 13%;
  text-align: center;
}

.colorAttributes img {
  padding: 3px;
}

.colorAttributes span {
  font-size: 12px;
  font-weight: 500;
  border-top: 1px solid #cacaca;
  padding: 2px 16px 0px 16px;
}

.colorAttributes:hover {
  border: 1px solid #f6986c;
  background-color: #f881330d;
}
.descSec {
  float: left;
  width: 100%;
  margin-top: 20px;
  overflow: hidden;
}
.descSec ul {
  margin: 0px 10px 0px 20px !important;
}
.contenido_indexado {
  float: left;
  width: 100%;
  margin: 20px 0px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 25px;
}
/* Safari */
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.terms-privacy-modal {
  max-width: 70% !important;
}

table {
  float: left;
  width: 100%;
  padding: 20px 0;
  margin: 0px;
}

.btn-primary {
  color: #fff;
  background-color: #20bced !important;
  border-color: #20bced !important;
}

.pager li > a,
.pager li > span {
  border-radius: 5px !important;
  color: red !important;
}

.pager {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}

@media (max-width: 768px) {
  .pager {
    flex-direction: column;
  }
}

.progress {
  height: 25px !important;
  margin-bottom: 10px;
  float: left;
  width: 65%;
}

.progress .skill {
  font: normal 12px "Open Sans Web";
  line-height: 25px;
  padding: 0;
  margin: 0 0 0 10px;
  text-transform: uppercase;
}

.progress .skill .val {
  float: right;
  font-style: normal;
  margin: 0 10px 0 0;
}

.progress-bar-line {
  text-align: left !important;
  transition-duration: 3s;
}

.bsp_big-image {
  box-shadow: 1px 1px 5px 1px rgba(0, 0, 0, 0);
  border-radius: 5px;
  margin-top: 0px;
}

@font-face {
  font-family: "Glyphicons Halflings";

  src: url("../fonts/glyphicons-halflings-regular.eot");
  src: url("../fonts/glyphicons-halflings-regular.eot?#iefix")
      format("embedded-opentype"),
    url("../fonts/glyphicons-halflings-regular.woff2") format("woff2"),
    url("../fonts/glyphicons-halflings-regular.woff") format("woff"),
    url("../fonts/glyphicons-halflings-regular.ttf") format("truetype"),
    url("../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular")
      format("svg");
}

.regular_price {
  margin-bottom: 7px;
  font-size: 17px;
  color: rgb(102, 100, 100);
  float: left;
  width: 100%;
}
.save_price {
  font-size: 15px;
  font-weight: 400;
  color: #09720c;
  position: relative;
  top: -6px;
  background: #b8f8ddbf;
  padding: 2px 10px 4px 10px;
  border-radius: 3px;
  margin-left: 10px;
}
.coupontxt {
  font-size: 9.7px;
  color: #05741d;
  background: #38f39436;
  display: block;
  padding: 1px 5px;
  text-align: center;
  margin-top: 3px;
  float: left;
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  font-style: normal;
  font-weight: normal;
  line-height: 1;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glyphicon-asterisk:before {
  content: "\2a";
}

.glyphicon-plus:before {
  content: "\2b";
}

.glyphicon-euro:before,
.glyphicon-eur:before {
  content: "\20ac";
}

.glyphicon-minus:before {
  content: "\2212";
}

.glyphicon-cloud:before {
  content: "\2601";
}

.glyphicon-envelope:before {
  content: "\2709";
}

.glyphicon-pencil:before {
  content: "\270f";
}

.glyphicon-glass:before {
  content: "\e001";
}

.glyphicon-music:before {
  content: "\e002";
}

.glyphicon-search:before {
  content: "\e003";
}

.glyphicon-heart:before {
  content: "\e005";
}

.glyphicon-star:before {
  content: "\e006";
}

.glyphicon-star-empty:before {
  content: "\e007";
}

.glyphicon-user:before {
  content: "\e008";
}

.glyphicon-film:before {
  content: "\e009";
}

.glyphicon-th-large:before {
  content: "\e010";
}

.glyphicon-th:before {
  content: "\e011";
}

.glyphicon-th-list:before {
  content: "\e012";
}

.glyphicon-ok:before {
  content: "\e013";
}

.glyphicon-remove:before {
  content: "\e014";
}

.glyphicon-zoom-in:before {
  content: "\e015";
}

.glyphicon-zoom-out:before {
  content: "\e016";
}

.glyphicon-off:before {
  content: "\e017";
}

.glyphicon-signal:before {
  content: "\e018";
}

.glyphicon-cog:before {
  content: "\e019";
}

.glyphicon-trash:before {
  content: "\e020";
}

.glyphicon-home:before {
  content: "\e021";
}

.glyphicon-file:before {
  content: "\e022";
}

.glyphicon-time:before {
  content: "\e023";
}

.glyphicon-road:before {
  content: "\e024";
}

.glyphicon-download-alt:before {
  content: "\e025";
}

.glyphicon-download:before {
  content: "\e026";
}

.glyphicon-upload:before {
  content: "\e027";
}

.glyphicon-inbox:before {
  content: "\e028";
}

.glyphicon-play-circle:before {
  content: "\e029";
}

.glyphicon-repeat:before {
  content: "\e030";
}

.glyphicon-refresh:before {
  content: "\e031";
}

.glyphicon-list-alt:before {
  content: "\e032";
}

.glyphicon-lock:before {
  content: "\e033";
}

.glyphicon-flag:before {
  content: "\e034";
}

.glyphicon-headphones:before {
  content: "\e035";
}

.glyphicon-volume-off:before {
  content: "\e036";
}

.glyphicon-volume-down:before {
  content: "\e037";
}

.glyphicon-volume-up:before {
  content: "\e038";
}

.glyphicon-qrcode:before {
  content: "\e039";
}

.glyphicon-barcode:before {
  content: "\e040";
}

.glyphicon-tag:before {
  content: "\e041";
}

.glyphicon-tags:before {
  content: "\e042";
}

.glyphicon-book:before {
  content: "\e043";
}

.glyphicon-bookmark:before {
  content: "\e044";
}

.glyphicon-print:before {
  content: "\e045";
}

.glyphicon-camera:before {
  content: "\e046";
}

.glyphicon-font:before {
  content: "\e047";
}

.glyphicon-bold:before {
  content: "\e048";
}

.glyphicon-italic:before {
  content: "\e049";
}

.glyphicon-text-height:before {
  content: "\e050";
}

.glyphicon-text-width:before {
  content: "\e051";
}

.glyphicon-align-left:before {
  content: "\e052";
}

.glyphicon-align-center:before {
  content: "\e053";
}

.glyphicon-align-right:before {
  content: "\e054";
}

.glyphicon-align-justify:before {
  content: "\e055";
}

.glyphicon-list:before {
  content: "\e056";
}

.glyphicon-indent-left:before {
  content: "\e057";
}

.glyphicon-indent-right:before {
  content: "\e058";
}

.glyphicon-facetime-video:before {
  content: "\e059";
}

.glyphicon-picture:before {
  content: "\e060";
}

.glyphicon-map-marker:before {
  content: "\e062";
}

.glyphicon-adjust:before {
  content: "\e063";
}

.glyphicon-tint:before {
  content: "\e064";
}

.glyphicon-edit:before {
  content: "\e065";
}

.glyphicon-share:before {
  content: "\e066";
}

.glyphicon-check:before {
  content: "\e067";
}

.glyphicon-move:before {
  content: "\e068";
}

.glyphicon-step-backward:before {
  content: "\e069";
}

.glyphicon-fast-backward:before {
  content: "\e070";
}

.glyphicon-backward:before {
  content: "\e071";
}

.glyphicon-play:before {
  content: "\e072";
}

.glyphicon-pause:before {
  content: "\e073";
}

.glyphicon-stop:before {
  content: "\e074";
}

.glyphicon-forward:before {
  content: "\e075";
}

.glyphicon-fast-forward:before {
  content: "\e076";
}

.glyphicon-step-forward:before {
  content: "\e077";
}

.glyphicon-eject:before {
  content: "\e078";
}

.glyphicon-chevron-left:before {
  content: "\e079";
}

.glyphicon-chevron-right:before {
  content: "\e080";
}

.glyphicon-plus-sign:before {
  content: "\e081";
}

.glyphicon-minus-sign:before {
  content: "\e082";
}

.glyphicon-remove-sign:before {
  content: "\e083";
}

.glyphicon-ok-sign:before {
  content: "\e084";
}

.glyphicon-question-sign:before {
  content: "\e085";
}

.glyphicon-info-sign:before {
  content: "\e086";
}

.glyphicon-screenshot:before {
  content: "\e087";
}

.glyphicon-remove-circle:before {
  content: "\e088";
}

.glyphicon-ok-circle:before {
  content: "\e089";
}

.glyphicon-ban-circle:before {
  content: "\e090";
}

.glyphicon-arrow-left:before {
  content: "\e091";
}

.glyphicon-arrow-right:before {
  content: "\e092";
}

.glyphicon-arrow-up:before {
  content: "\e093";
}

.glyphicon-arrow-down:before {
  content: "\e094";
}

.glyphicon-share-alt:before {
  content: "\e095";
}

.glyphicon-resize-full:before {
  content: "\e096";
}

.glyphicon-resize-small:before {
  content: "\e097";
}

.glyphicon-exclamation-sign:before {
  content: "\e101";
}

.glyphicon-gift:before {
  content: "\e102";
}

.glyphicon-leaf:before {
  content: "\e103";
}

.glyphicon-fire:before {
  content: "\e104";
}

.glyphicon-eye-open:before {
  content: "\e105";
}

.glyphicon-eye-close:before {
  content: "\e106";
}

.glyphicon-warning-sign:before {
  content: "\e107";
}

.glyphicon-plane:before {
  content: "\e108";
}

.glyphicon-calendar:before {
  content: "\e109";
}

.glyphicon-random:before {
  content: "\e110";
}

.glyphicon-comment:before {
  content: "\e111";
}

.glyphicon-magnet:before {
  content: "\e112";
}

.glyphicon-chevron-up:before {
  content: "\e113";
}

.glyphicon-chevron-down:before {
  content: "\e114";
}

.glyphicon-retweet:before {
  content: "\e115";
}

.glyphicon-shopping-cart:before {
  content: "\e116";
}

.glyphicon-folder-close:before {
  content: "\e117";
}

.glyphicon-folder-open:before {
  content: "\e118";
}

.glyphicon-resize-vertical:before {
  content: "\e119";
}

.glyphicon-resize-horizontal:before {
  content: "\e120";
}

.glyphicon-hdd:before {
  content: "\e121";
}

.glyphicon-bullhorn:before {
  content: "\e122";
}

.glyphicon-bell:before {
  content: "\e123";
}

.glyphicon-certificate:before {
  content: "\e124";
}

.glyphicon-thumbs-up:before {
  content: "\e125";
}

.glyphicon-thumbs-down:before {
  content: "\e126";
}

.glyphicon-hand-right:before {
  content: "\e127";
}

.glyphicon-hand-left:before {
  content: "\e128";
}

.glyphicon-hand-up:before {
  content: "\e129";
}

.glyphicon-hand-down:before {
  content: "\e130";
}

.glyphicon-circle-arrow-right:before {
  content: "\e131";
}

.glyphicon-circle-arrow-left:before {
  content: "\e132";
}

.glyphicon-circle-arrow-up:before {
  content: "\e133";
}

.glyphicon-circle-arrow-down:before {
  content: "\e134";
}

.glyphicon-globe:before {
  content: "\e135";
}

.glyphicon-wrench:before {
  content: "\e136";
}

.glyphicon-tasks:before {
  content: "\e137";
}

.glyphicon-filter:before {
  content: "\e138";
}

.glyphicon-briefcase:before {
  content: "\e139";
}

.glyphicon-fullscreen:before {
  content: "\e140";
}

.glyphicon-dashboard:before {
  content: "\e141";
}

.glyphicon-paperclip:before {
  content: "\e142";
}

.glyphicon-heart-empty:before {
  content: "\e143";
}

.glyphicon-link:before {
  content: "\e144";
}

.glyphicon-phone:before {
  content: "\e145";
}

.glyphicon-pushpin:before {
  content: "\e146";
}

.glyphicon-usd:before {
  content: "\e148";
}

.glyphicon-gbp:before {
  content: "\e149";
}

.glyphicon-sort:before {
  content: "\e150";
}

.glyphicon-sort-by-alphabet:before {
  content: "\e151";
}

.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152";
}

.glyphicon-sort-by-order:before {
  content: "\e153";
}

.glyphicon-sort-by-order-alt:before {
  content: "\e154";
}

.glyphicon-sort-by-attributes:before {
  content: "\e155";
}

.glyphicon-sort-by-attributes-alt:before {
  content: "\e156";
}

.glyphicon-unchecked:before {
  content: "\e157";
}

.glyphicon-expand:before {
  content: "\e158";
}

.glyphicon-collapse-down:before {
  content: "\e159";
}

.glyphicon-collapse-up:before {
  content: "\e160";
}

.glyphicon-log-in:before {
  content: "\e161";
}

.glyphicon-flash:before {
  content: "\e162";
}

.glyphicon-log-out:before {
  content: "\e163";
}

.glyphicon-new-window:before {
  content: "\e164";
}

.glyphicon-record:before {
  content: "\e165";
}

.glyphicon-save:before {
  content: "\e166";
}

.glyphicon-open:before {
  content: "\e167";
}

.glyphicon-saved:before {
  content: "\e168";
}

.glyphicon-import:before {
  content: "\e169";
}

.glyphicon-export:before {
  content: "\e170";
}

.glyphicon-send:before {
  content: "\e171";
}

.glyphicon-floppy-disk:before {
  content: "\e172";
}

.glyphicon-floppy-saved:before {
  content: "\e173";
}

.glyphicon-floppy-remove:before {
  content: "\e174";
}

.glyphicon-floppy-save:before {
  content: "\e175";
}

.glyphicon-floppy-open:before {
  content: "\e176";
}

.glyphicon-credit-card:before {
  content: "\e177";
}

.glyphicon-transfer:before {
  content: "\e178";
}

.glyphicon-cutlery:before {
  content: "\e179";
}

.glyphicon-header:before {
  content: "\e180";
}

.glyphicon-compressed:before {
  content: "\e181";
}

.glyphicon-earphone:before {
  content: "\e182";
}

.glyphicon-phone-alt:before {
  content: "\e183";
}

.glyphicon-tower:before {
  content: "\e184";
}

.glyphicon-stats:before {
  content: "\e185";
}

.glyphicon-sd-video:before {
  content: "\e186";
}

.glyphicon-hd-video:before {
  content: "\e187";
}

.glyphicon-subtitles:before {
  content: "\e188";
}

.glyphicon-sound-stereo:before {
  content: "\e189";
}

.glyphicon-sound-dolby:before {
  content: "\e190";
}

.glyphicon-sound-5-1:before {
  content: "\e191";
}

.glyphicon-sound-6-1:before {
  content: "\e192";
}

.glyphicon-sound-7-1:before {
  content: "\e193";
}

.glyphicon-copyright-mark:before {
  content: "\e194";
}

.glyphicon-registration-mark:before {
  content: "\e195";
}

.glyphicon-cloud-download:before {
  content: "\e197";
}

.glyphicon-cloud-upload:before {
  content: "\e198";
}

.glyphicon-tree-conifer:before {
  content: "\e199";
}

.glyphicon-tree-deciduous:before {
  content: "\e200";
}

.glyphicon-cd:before {
  content: "\e201";
}

.glyphicon-save-file:before {
  content: "\e202";
}

.glyphicon-open-file:before {
  content: "\e203";
}

.glyphicon-level-up:before {
  content: "\e204";
}

.glyphicon-copy:before {
  content: "\e205";
}

.glyphicon-paste:before {
  content: "\e206";
}

.glyphicon-alert:before {
  content: "\e209";
}

.glyphicon-equalizer:before {
  content: "\e210";
}

.glyphicon-king:before {
  content: "\e211";
}

.glyphicon-queen:before {
  content: "\e212";
}

.glyphicon-pawn:before {
  content: "\e213";
}

.glyphicon-bishop:before {
  content: "\e214";
}

.glyphicon-knight:before {
  content: "\e215";
}

.glyphicon-baby-formula:before {
  content: "\e216";
}

.glyphicon-tent:before {
  content: "\26fa";
}

.glyphicon-blackboard:before {
  content: "\e218";
}

.glyphicon-bed:before {
  content: "\e219";
}

.glyphicon-apple:before {
  content: "\f8ff";
}

.glyphicon-erase:before {
  content: "\e221";
}

.glyphicon-hourglass:before {
  content: "\231b";
}

.glyphicon-lamp:before {
  content: "\e223";
}

.glyphicon-duplicate:before {
  content: "\e224";
}

.glyphicon-piggy-bank:before {
  content: "\e225";
}

.glyphicon-scissors:before {
  content: "\e226";
}

.glyphicon-bitcoin:before {
  content: "\e227";
}

.glyphicon-btc:before {
  content: "\e227";
}

.glyphicon-xbt:before {
  content: "\e227";
}

.glyphicon-yen:before {
  content: "\00a5";
}

.glyphicon-jpy:before {
  content: "\00a5";
}

.glyphicon-ruble:before {
  content: "\20bd";
}

.glyphicon-rub:before {
  content: "\20bd";
}

.glyphicon-scale:before {
  content: "\e230";
}

.glyphicon-ice-lolly:before {
  content: "\e231";
}

.glyphicon-ice-lolly-tasted:before {
  content: "\e232";
}

.glyphicon-education:before {
  content: "\e233";
}

.glyphicon-option-horizontal:before {
  content: "\e234";
}

.glyphicon-option-vertical:before {
  content: "\e235";
}

.glyphicon-menu-hamburger:before {
  content: "\e236";
}

.glyphicon-modal-window:before {
  content: "\e237";
}

.glyphicon-oil:before {
  content: "\e238";
}

.glyphicon-grain:before {
  content: "\e239";
}

.glyphicon-sunglasses:before {
  content: "\e240";
}

.glyphicon-text-size:before {
  content: "\e241";
}

.glyphicon-text-color:before {
  content: "\e242";
}

.glyphicon-text-background:before {
  content: "\e243";
}

.glyphicon-object-align-top:before {
  content: "\e244";
}

.glyphicon-object-align-bottom:before {
  content: "\e245";
}

.glyphicon-object-align-horizontal:before {
  content: "\e246";
}

.glyphicon-object-align-left:before {
  content: "\e247";
}

.glyphicon-object-align-vertical:before {
  content: "\e248";
}

.glyphicon-object-align-right:before {
  content: "\e249";
}

.glyphicon-triangle-right:before {
  content: "\e250";
}

.glyphicon-triangle-left:before {
  content: "\e251";
}

.glyphicon-triangle-bottom:before {
  content: "\e252";
}

.glyphicon-triangle-top:before {
  content: "\e253";
}

.glyphicon-console:before {
  content: "\e254";
}

.glyphicon-superscript:before {
  content: "\e255";
}

.glyphicon-subscript:before {
  content: "\e256";
}

.glyphicon-menu-left:before {
  content: "\e257";
}

.glyphicon-menu-right:before {
  content: "\e258";
}

.glyphicon-menu-down:before {
  content: "\e259";
}

.glyphicon-menu-up:before {
  content: "\e260";
}

.glyphicon-star {
  font-size: 25px;
  color: #e67e22;
  margin-right: 6px;
}
.half {
  position: relative;
}
.half:before {
  position: relative;
  z-index: 9;
  width: 47%;
  display: block;
  overflow: hidden;
}
.half:after {
  content: "\e006";
  position: absolute;
  z-index: 8;
  color: #bdc3c7;
  top: 0;
  left: 0;
}

tr.CartProduct {
  border-bottom: 1px solid #e7e9ec;
}
.cartTableHeader {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 14px;
}

.cartTableHeader {
  background: #ebedef;
}
tr.CartProduct td,
tr.CartProduct th {
  padding: 5px 10px;
}
tr.CartProduct td,
tr.CartProduct th {
  text-align: center;
}
tr.CartProduct td:nth-child(2) {
  text-align: left;
}
.fa-2x {
  font-size: 1.5em;
  color: #e51010;
}
.bootstrap-touchspin-prefix:empty,
.bootstrap-touchspin-postfix:empty {
  display: none;
}

th:last-child,
td:last-child {
  text-align: right;
}

.cartTable input[type="text"],
.cartTable input[type="password"],
.cartTable input[type="email"] {
  border-color: #dddddd;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  border-style: solid;
  border-width: 1px;
  color: #888888;
  font-size: 14px;
  margin-bottom: 0px;
  height: 36px;
}
.CartDescription h4 {
  font-size: 16px;
  font-weight: bold;
}
.customer_prof {
  margin-right: 10px;
  width: 30px;
  background: #d1d1d1;
  padding: 5px 6px;
  border-radius: 16px;
}

@media screen and (max-height: 450px) {
  .sidenav {
    padding-top: 15px;
  }
  .sidenav a {
    font-size: 15px;
  }
}

@media screen and (max-width: 767px) {
  .creditCard img {
    position: relative;
    top: -5px !important;
  }

  .registration_form {
    /* max-height: 300px;
    overflow-y: auto;
    float: left;
    margin-bottom: 30px; */
  }

  .login_form {
    margin-bottom: 30px;
    float: left;
    width: 100%;
  }

  .reg-btn-wrap button {
    width: 143px;
    display: block;
    margin: 0 auto;
    background: #fe0000;
    font-weight: 700;
    font-size: 15px;
    text-transform: uppercase;
    border-radius: 3px;
    color: #fff;
    margin-bottom: 10px;
    margin-top: 10px;
    float: right;
    padding: 10px 0;
  }

  .signup-btn-wrap button {
    width: 154px;
  }

  .star-rating {
    padding: 0 0px !important;
    color: #ccc;
    width: 100% !important;
    float: left !important;
  }

  .star-rating label {
    color: #bbb;
    font-size: 18px;
    padding: 0;
    cursor: pointer;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    float: right !important;
  }
  .star-rating input[type="radio"] {
    display: none;
  }
  .star-rating label:hover,
  .star-rating label:hover ~ label,
  .star-rating input[type="radio"]:checked ~ label {
    color: orange;
  }

  .details-container ul li {
    float: left;
    width: 95% !important;
    margin: 0 10px 5px 0;
    list-style-type: square;
    padding: 0px;
    color: #333;
  }

  .navbar-nav .dropdown-menu {
    position: relative !important;
    float: left !important;
    width: 300px;
    top: 0 !important;
    height: 100%;
    max-height: 300px;
    overflow-y: auto;
  }

  .autocomplete {
    border: 1px solid #ccc;
    position: absolute;
    background: #fff;
    z-index: 999999;
    padding: 15px;
    margin-top: 65px;
    max-width: 343px;
    max-height: 290px;
    overflow-y: auto;
  }

  .customer-logos .slick-list {
    overflow: hidden;
    margin: 0 0px 0 10px !important;
    padding: 0;
  }
  .deal-link-item figure img {
    max-width: 100%;
    height: auto; /* max-height:120px; */
  }

  .slick-next,
  .slick-prev {
    display: none !important;
  }

  .main-menu ul ul {
    right: 0 !important;
  }

  .deal-link-item {
    margin-left: 0;
  }

  .table > tbody > tr > td,
  .table > tbody > tr > th,
  .table > tfoot > tr > td,
  .table > tfoot > tr > th,
  .table > thead > tr > td,
  .table > thead > tr > th {
    padding: 5px 0 5px 5px !important;
  }

  .table > tbody > tr > td {
    padding: 5px 0 5px 5px !important;
  }
  .banner-container figure {
    width: 100%;
    min-height: 120px;
  }

  .banner-container figure img {
    width: 100%;
    min-height: 120px;
  }

  .banner-container .product-title {
    float: none;
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 21px;
    font-weight: 700;
    padding: 50px 0;
    text-transform: uppercase;
    color: #fff;
  }

  .filter-icon {
    float: right;
    width: 40px;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #ccc;
    overflow: hidden;
    margin-right: 0px;
    margin-top: 20px;
  }

  .filter select {
    background: #fff;
    border: 1px solid #ccc;
    margin-bottom: 10px;
    height: 30px;
    margin-right: 5px;
    font-size: 14px;
    line-height: 14px;
    border-radius: 3px;
    width: 100%;
  }

  .filter input[type="submit"] {
    background-color: #20bced;
    border: none;
    text-decoration: none;
    color: white;
    padding: 3px 10px;
    margin-right: 5px;
    cursor: pointer;
    border-radius: 3px;
    margin-bottom: 10px;
  }
  .submenu {
    overflow: hidden;
    max-height: 0;
    -webkit-transition: all 0.5s ease-out;
    position: absolute;
    z-index: 999;
    width: 92%;
    right: 15px;
    right: 0px;
    margin-top: 0px;
  }

  .submenu a {
    background-color: #20bced;
  }

  /* hover behaviour for links inside .submenu */
  .submenu a:hover {
    background-color: #17a5d1;
  }

  .addToQuote {
    margin-left: 0px !important;
    background-color: #f44336 !important;
  }
  .signup-btn-wrap button {
    width: 155px !important;
  }
}

/*  popup css */

/* .popupbg{background:#fff url(../images/popupbg.png) no-repeat -2px center !important; background-size:cover; min-height:537px;} */

.signup-btn-wrap button {
  width: 168px;
  display: block;
  margin: 0 auto;
  background: #20bced;
  font-weight: 700;
  font-size: 15px;
  text-transform: uppercase;
  border-radius: 3px;
  color: #fff;
  margin-bottom: 10px;
  margin-top: 10px;
  float: left;
  padding: 10px 0;
}
.signup-btn-wrap button:hover,
.signup-btn-wrap button.btn.red-bg:hover {
  background: #fe0000;
  color: #fff;
}

.reg-btn-wrap button {
  width: 168px;
  display: block;
  margin: 0 auto;
  background: #fe0000;
  font-weight: 700;
  font-size: 15px;
  text-transform: uppercase;
  border-radius: 3px;
  color: #fff;
  margin-bottom: 10px;
  margin-top: 10px;
  float: right;
  padding: 10px 0;
}
.reg-btn-wrap button:hover,
.reg-btn-wrap button.btn.red-bg:hover {
  background: #0065f5;
  color: #fff;
}

/* =================== */

.slide-animation,
.slide-animation2,
.slide-animation3,
.slide-animation4 {
  position: relative;
  left: -70px;
  -webkit-animation: slide 0.5s forwards;
  animation: slide 0.5s forwards;
}

.slide-animation2 {
  position: relative;
  left: -70px;
  -webkit-animation: slide 0.5s forwards;
  animation: slide 0.5s forwards;
}

.slide-animation3 {
  position: relative;
  left: -70px;
  -webkit-animation: slide 0.5s forwards;
  animation: slide 0.5s forwards;
}

.slide-animation4 {
  position: relative;
  left: -70px;
  -webkit-animation: slide 0.5s forwards;
  animation: slide 0.5s forwards;
}

.addToQuote {
  margin-top: 0px;
  background-color: #f44336 !important;
}

@-webkit-keyframes slide {
  100% {
    left: 0;
  }
}

@keyframes slide {
  100% {
    left: 0;
  }
}

.card {
  cursor: pointer;
  box-shadow: 0px 0px 5px #ccc;
}
.card .card-block {
  padding: 1.25rem;
}
.card label {
  font-size: 14px;
  font-weight: 600;
}
.card-desc h6 {
  font-size: 18px;
}
.f-w-700 {
  font-weight: 400;
  font-size: 15px;
}
.m-t-20 {
  margin-top: 20px;
}
.m-b-20 {
  margin-bottom: 20px;
}
.m-b-30 {
  margin-bottom: 30px;
}
.hrt {
  border-top: 2px solid #d2d2d2;
  width: 55px;
}

@media (max-width: 767px) {
  .upsells-container .heading-wrap button {
    border-radius: 2px;
    width: 66% !important;
    background-color: #5c8cf8 !important;
    color: #fff;
    float: left;
    font-size: 13px !important;
    margin-right: 0px;
    padding: 5px;
  }
  .upsells-container .upsell-product-item .heading-wrap h2 {
    margin-top: 20px !important;
    width: 62%;
    float: left;
    font-size: 15px !important;
    line-height: 25px !important;
  }

  .upsells-container .upsell-product-item .heading-wrap .price {
    margin-top: 55px !important;
    width: 34%;
    float: right;
    font-size: 16px !important;
    font-weight: bold !important;
    text-align: right;
    color: #f44336;
  }
  .upsells-container .upsell-product-item .heading-wrap {
    margin: 0 !important;
    width: 63%;
    float: left;
  }

  .upsells-container .upsell-product-item figure {
    margin: 0 !important;
    width: 37%;
    float: left;
    padding: 10px 15px 10px 15px !important;
  }

  .input-group {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    width: 100%;
  }

  .cartContent td:last-child {
    text-align: right !important;
    width: 120px;
    float: right;
  }

  .cartMiniTabletd:last-child {
    text-align: right !important;
    float: none;
  }

  .feedback-stars {
    position: relative;
    float: left;
    margin-top: 10px;
    width: 66%;
    left: 52px;
  }

  .modal-dialog {
    width: 96%;
    margin: 30px auto;
  }
  .modal-body {
    position: relative;
    padding: 0px;
  }

  .right-form-logo {
    text-align: center;
    float: left;
    width: 100%;
    padding: 20px 0 20px 0;
  }
  .right-form-area {
    text-align: left;
    padding-left: 20px;
    float: left;
  }
  .popupbg {
    background: #fff !important;
    background-size: cover;
    min-height: 537px;
    float: left;
  }
  .popupbg1 {
    background: #fff url(../images/popupbg1.png) no-repeat -2px center !important;
    background-size: cover;
    min-height: 100px;
    width: 100%;
    float: left;
  }
  .modal-dialog h2 {
    font-size: 30px;
    font-weight: 900;
    color: #fff;
    text-align: center;
    margin: 20px 0 5px 0;
  }

  .modal-dialog h3 {
    font-size: 20px;
    font-weight: 400;
    color: #fff;
    text-align: center;
    margin: 0px 0 10px 0;
  }

  .main-menu-container {
    display: none;
  }

  .offer-tab a {
    background: red;
    color: #fff !important;
    font-size: 11px;
    padding: 5px 4px;
    float: right;
    width: 100%;
    border-radius: 3px;
  }
  .offer-tab a:hover {
    background: black;
    color: #fff !important;
    padding: 5px 10px;
    float: right;
    width: 100%;
  }

  .offer-tab img {
    margin: 3px 8px 0 0 !important;
    width: 24% !important;
    float: left;
  }
  .navbar-nav:not(.mobile-navbar-nav) .dropdown-menu li ul {
    position: relative;
    display: block;
    background: #fff !important;
    border: 0px solid #ccc;
    padding: 0px;
    margin: 0px 0 0 20px;
    left: 0px;
    width: 300px;
  }

  .navbar-nav:not(.mobile-navbar-nav) .dropdown-menu li:hover ul {
    position: relative;
    display: block;
    background: #fff !important;
    border: 0px solid #ccc;
    padding: 0px;
    margin: 0px 0 0 24px;
    left: 0px;
    width: 300px;
  }

  .slider-container {
    min-height: auto;
  }

  .slider-container .left_banner_section {
    width: 100%;
    height: auto;
  }

  .slider-container .right_banner_section {
    width: 100%;
    margin-bottom: 10px;
  }

  .slider-container .left_banner_section img {
    height: auto;
    min-height: auto;
    max-height: auto;
  }

  .slider-container .right_banner_section img {
    width: 100%;
    min-height: 175px;
    height: auto;
    max-height: 175px;
    margin-bottom: 30px;
  }
  .col-xs-6 {
    width: 50% !important;
  }
  .offer-link-item {
    border-left: none !important;
  }

  .mobile .search_box_area {
    float: left;
    width: 100%;
    margin-top: 5px;
    margin-bottom: 15px;
  }
  .mobile .header-top-section ul li {
    margin-right: 8px;
  }

  .mobile .icon-float {
    float: left;
    margin-right: 7px;
    width: 22px;
  }

  .mobile .nav > li .icon-float {
    margin-top: 0 !important;
  }

  .mobile .main-menu {
    z-index: 99999999999;
    position: relative;
    float: right;
  }
  .mobile .logo-area {
    display: table;
    height: 78px;
    position: relative;
    z-index: 99999;
  }
  .mobile .logo-area img {
    max-width: 100%;
    height: auto;
    margin-top: 0px;
  }
  .product-link-item img {
    height: 50px !important;
  }

  .slick-prev {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: absolute;
    overflow: hidden;
    background: #fff !important;
    border: 1px solid #ccc;
    top: 40%;
    z-index: 999999;
    left: 4px;
  }
  .slick-next {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: absolute;
    overflow: hidden;
    background: #fff !important;
    border: 1px solid #ccc;
    top: 40%;
    z-index: 999999;
    right: 4px;
  }

  .right_banner_section .slick-next,
  .slick-next:focus,
  .right_banner_section .slick-next:hover,
  .right_banner_section .slick-prev,
  .right_banner_section.slick-prev:focus,
  .right_banner_section .slick-prev:hover {
    background: transparent !important;
  }

  .cart-counter {
    width: 20px;
    height: 20px;
    position: absolute;
    border-radius: 50%;
    overflow: hidden;
    background: #ff002a;
    color: #fff;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    margin: 0px 0px 0 0;
    left: 19px;
  }

  .slide-text {
    margin: 0;
    padding: 10px;
    position: relative;
    text-align: left;
    padding: 10px 30px 10px 30px;
    width: 100%;
    float: left;
  }

  .slide-image-cell {
    margin: 0;
    padding: 10px;
    position: relative;
    text-align: left;
    padding: 10px 30px 10px 30px;
    width: 100%;
    float: left;
  }
}

@media only screen and (max-width: 1015px) and (min-width: 768px) {
  .main-menu ul li a {
    font-size: 10px;
  }
  .heading-wrap {
    float: left;
    width: 147px;
  }

  .category-container .col-md-2 {
    flex: 0 0 32.666667%;
    max-width: 32.666667%;
  }

  .slick-slide img {
    width: 100%;
    max-height: 250px;
  }
}

.LG-brandpage {
  max-width: 975px;
  margin: 0 auto;
}
.cont-2 {
  width: 100%;
  /*min-height: 600px;
    border: 1px solid black;
    padding:1px;*/
}
.box-a {
  width: 49%;
  float: left;
  /*background-color: chartreuse;*/
  margin: 0px 4px;
}
.cont-3 {
  min-height: 200px;
  width: 100%;
  /*border: 1px solid black;*/
}
.box-c {
  width: 32%;
  float: left;
  padding: 0px;
  /*background-color: rgb(121, 36, 179);*/
  min-height: 300px;
  margin: 6px;
}

.responsive {
  width: 100%;
  height: auto;
}

.cont-3 li {
  margin: 0px 5px;
  width: 30%;
  float: left;
  /*background-color: blueviolet;*/
  list-style: none;
  min-height: 200px;
}
@media only screen and (max-width: 620px) {
  /* For mobile phones: */
  .itemfull {
  }
  .box-a {
    width: 100%;
    float: left;
    /*background-color: chartreuse;*/
    margin: 5px;
  }
  .box-c {
    width: 100%;
    float: left;
    padding: 0px;
    /*background-color: rgb(121, 36, 179);*/
    min-height: 300px;
    margin: 6px;
  }
}

#g-BrandShop_editable-box,
#g-BrandShop_editable-box .g-artboard {
  margin: 0 auto;
}
#g-BrandShop_editable-box p {
  margin: 0;
}
#g-BrandShop_editable-box .g-aiAbs {
  position: absolute;
}
#g-BrandShop_editable-box .g-aiImg {
  position: absolute;
  top: 0;
  display: block;
  width: 100% !important;
}
#g-BrandShop_editable-box .g-aiSymbol {
  position: absolute;
  box-sizing: border-box;
}
#g-BrandShop_editable-box .g-aiPointText p {
  white-space: nowrap;
}
#g-BrandShop_editable-Artboard_1 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_2 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_3 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_4 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_5 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_6 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_7 {
  position: relative;
  overflow: hidden;
}
#g-BrandShop_editable-Artboard_1_copy_8 {
  position: relative;
  overflow: hidden;
}

figure {
  line-height: 0;
}

.section .flex img {
  width: auto;
  height: auto;
  max-width: 100%;
}

.flex {
  display: flex;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

.noWrap {
  -o-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.justify_between {
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  -o-justify-content: space-between;
  justify-content: space-between;
}

.justify_center {
  -moz-justify-content: center;
  -ms-justify-content: center;
  -o-justify-content: center;
  justify-content: center;
}

.justify_around {
  -moz-justify-content: space-around;
  -ms-justify-content: space-around;
  -o-justify-content: space-around;
  justify-content: space-around;
}

.justify_end {
  -moz-justify-content: flex-end;
  -ms-justify-content: flex-end;
  -o-justify-content: flex-end;
  justify-content: flex-end;
}

.direcction_column {
  -moz-flex-direction: column;
  -o-flex-direction: column;
  flex-direction: column;
}

.align_items_stretch {
  -o-align-items: stretch;
  -ms-align-items: stretch;
  -moz-align-items: stretch;
  align-items: stretch;
}

.align_items_start {
  -moz-align-items: flex-start;
  -ms-align-items: flex-start;
  -o-align-items: flex-start;
  align-items: flex-start;
}

.align_items_end {
  -moz-align-items: flex-end;
  -ms-align-items: flex-end;
  -o-align-items: flex-end;
  align-items: flex-end;
}

.align_self_start {
  -o-align-self: flex-start;
  -ms-align-self: flex-start;
  align-self: flex-start;
}

.align_self_end {
  -o-align-self: flex-end;
  -ms-align-self: flex-end;
  align-self: flex-end;
}

.align_items_center {
  -moz-align-items: center;
  -ms-align-items: center;
  -o-align-items: center;
  align-items: center;
}

.align_content_start {
  -ms-align-content: flex-start;
  -o-align-content: flex-start;
  align-content: flex-start;
}

.align_content_center {
  -ms-align-content: center;
  -o-align-content: center;
  align-content: center;
}

.align_content_end {
  -ms-align-content: flex-end;
  -o-align-content: flex-end;
  align-content: flex-end;
}

.section {
  width: 100%;
  max-width: 1250px;
  margin: 0 auto;
  padding: 0 15px;
}

@media screen and (max-width: 1250px) {
  .section {
    padding: 0;
  }
}

.col1 {
  width: 100%;
  padding: 10px;
}

@media screen and (max-width: 1250px) {
  .col1 {
    padding: 10px 0;
  }
}

.col2 {
  width: 100%;
  max-width: 50%;
  padding: 10px;
}

.col3 {
  width: 100%;
  max-width: calc(100% / 3);
  padding: 10px;
}

@media screen and (max-width: 1024px) {
  .col2 {
    max-width: 100%;
    padding: 10px 0;
  }
  .col3 {
    max-width: 100%;
    padding: 10px 0;
  }
}
.w-100 {
  width: 100%;
}

.text-center {
  text-align: center;
}

.pb-0 {
  padding-bottom: 0;
}

.pt-0 {
  padding-top: 0;
}

.brandshop-section {
  float: left;
  width: 100%;
  margin-top: 10px;
}
.brandshop-section .container {
  max-width: 1170px;
  margin: 0px auto;
  text-align: center;
}
.brandshop-section .container img {
  max-width: 100%;
}
.imag-spacing {
}

.brandshop-section .container .product-img1 {
  max-height: 439px;
  width: 100%;
}

.brandshop-section .container .product-img2 {
  max-height: 367px;
  width: 100%;
}
.brandshop-section .container .product-img3 {
  width: 100%;
}

.brandshop-section .container .product-img4 {
  width: 100%;
}
.brandshop-section .container .product-img5 {
  width: 100%;
}

.brandshop-section .container .product-img11 {
  max-height: 693px;
  width: 100%;
}

.brandshop-section .container .product-img22 {
  max-height: 367px;
  width: 100%;
}
.brandshop-section .container .product-img33 {
  width: 100%;
}

.brandshop-section .container .product-img44 {
  width: 200%;
  max-width: 100%;
  height: 367px;
}

.brandshop-section .container .col-lg-1,
.brandshop-section .container .col-lg-10,
.brandshop-section .container .col-lg-11,
.brandshop-section .container .col-lg-12,
.brandshop-section .container .col-lg-2,
.brandshop-section .container .col-lg-3,
.brandshop-section .container .col-lg-4,
.brandshop-section .container .col-lg-5,
.brandshop-section .container .col-lg-6,
.brandshop-section .container .col-lg-7,
.brandshop-section .container .col-lg-8,
.brandshop-section .container .col-lg-9 {
  float: left;
  border-bottom: 15px solid #fff;
  border-left: 7px solid #fff;
  border-right: 7px solid #fff;
  margin: 0px;
  padding: 0px;
}
.brandshop-section .container .col-lg-12 {
  width: 100%;
}

.brandshop-section .container .col-lg-11 {
  width: 91.66666667%;
}

.brandshop-section .container .col-lg-10 {
  width: 83.33333333%;
}

.brandshop-section .container .col-lg-9 {
  width: 75%;
}

.brandshop-section .container .col-lg-8 {
  width: 66.66666667%;
}

.brandshop-section .container .col-lg-7 {
  width: 58.33333333%;
}

.brandshop-section .container .col-lg-6 {
  width: 50%;
}

.brandshop-section .container .col-lg-5 {
  width: 41.66666667%;
}

.brandshop-section .container .col-lg-4 {
  width: 33.33333333%;
}

.brandshop-section .container .col-lg-3 {
  width: 23%;
}

.brandshop-drija .container .col-lg-3 {
  width: 25%;
  margin: 15px 0px;
}

.brandshop-section .container .col-lg-2 {
  width: 16.66666667%;
}

.brandshop-section .container .col-lg-1 {
  width: 8.33333333%;
}

.clear-fx {
  float: left;
  width: 100%;
  clear: both;
}

.col-lg-3-brand {
  flex: 0 0 12.5% !important;
  max-width: 12.5% !important;
}

.col-lg-3-brand-full {
  flex: 0 0 20% !important;
  max-width: 20% !important;
}
.brandshop-section .container .drija-img-3 {
  height: 527px;
  width: 100%;
}

.frigidaire-img {
  /* padding-top: 25px !important;
  max-width: 105%; */
}

@media screen and (max-width: 767px) {
  .sub-banner-container-mobile {
    display: block;
  }
  .sub-banner-container-desktop {
    display: none;
  }
  .desktop {
    display: none;
  }
  .brandshop-section .container .drija-mp4-1 {
    width: 100%;
    height: 100%;
  }
  .brandshop-section .container .drija-mp4-2 {
    width: 100%;
    height: 100%;
  }
  .brandshop-section .container .drija-img-4 {
    min-height: 183px;
  }
  .brandshop-section .container .drija-img-3 {
    height: 100%;
    width: 100%;
  }
  .col-lg-3-brand {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
  .col-md-3-brand {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .col-xs-6-full {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }

  .brandshop-section .container .product-img1 {
    max-height: 439px;
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img2 {
    max-height: 367px;
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img3 {
    min-height: 170px;
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img4 {
    min-height: 120px;
    max-width: 100% !important;
    width: 100%;
  }
  .brandshop-section .container .product-img5 {
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img11 {
    max-height: 225px;
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img22 {
    max-height: 367px;
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img33 {
    min-height: 170px;
    max-width: 100% !important;
    width: 100%;
  }

  .brandshop-section .container .product-img44 {
    width: 200%;
    max-width: 100%;
    height: 119px;
  }

  .brandshop-section .container .col-sm-6 {
    width: 50% !important;
  }
  .brandshop-section .container .col-xs-6 {
    width: 50% !important;
  }
  .brandshop-section .container .col-sm-12 {
    width: 100% !important;
  }
  .brandshop-section .container .col-xs-12 {
    width: 100% !important;
  }
  .brandshop-section .container .col-lg-1,
  .brandshop-section .container .col-lg-10,
  .brandshop-section .container .col-lg-11,
  .brandshop-section .container .col-lg-12,
  .brandshop-section .container .col-lg-2,
  .brandshop-section .container .col-lg-3,
  .brandshop-section .container .col-lg-4,
  .brandshop-section .container .col-lg-5,
  .brandshop-section .container .col-lg-6,
  .brandshop-section .container .col-lg-7,
  .brandshop-section .container .col-lg-8,
  .brandshop-section .container .col-lg-9 {
    float: left;
    border-bottom: 15px solid #fff;
    border-left: 7px solid #fff;
    border-right: 7px solid #fff;
    margin: 0px;
    padding: 0px;
  }
}

.catagory-container-right .pagination li button {
    border: none;
    color: #3c525d;
    font-size: 14px;
}

.catagory-container-right .pagination li.active button {
    border-radius: 50%;
    color: #fff;
    padding: 2px;
    background-color: #0075b9;
    width: 25px;
    height: 25px;
    display: flex
;
    align-items: center;
    justify-content: center;
}

.catagory-container-right .pagination li button {
    border: none;
    color: #3c525d;
    font-size: 14px;
}
.pagination>li>button, .pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #337ab7;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
}
