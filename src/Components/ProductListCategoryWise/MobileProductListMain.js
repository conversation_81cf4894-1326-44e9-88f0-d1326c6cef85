import React, { useEffect, useState } from "react";
import API_BASE_URL from "../../config/api";
import { useParams } from "react-router-dom";
import { MobileProductListSidebar } from "./MobileProductListSidebar";
import { MobileProductListListing } from "./MobileProductListListing";
import ProductSearchAutocomplete from "../Header/ProductSearchAutocomplete";
import MobileProductFilterSection from "./MobileProductFilterSection";
import "./MobileProductListMain.module.css";
import { connect } from "react-redux";
import { 
  productsListByBrand, 
  productsListByAllowPurchase, 
  productsListByPriceRange, 
  priceFilterByType,
  resetFilterByPriceRange 
} from "../../Services/Actions/filter.actions";

export const MobileProductListMainComponent = ({
  brandFilter,
  allowPurchaseFilter,
  priceRangeFilter,
  priceFilterByType,
  priceRangeFilterReset
}) => {
  const {
    categoryName: paramCategoryName,
    subCategoryName: paramSubCategoryName,
    lastSubCategoryName: paramLastSubCategoryName,
  } = useParams();

  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState("");
  const [products, setProducts] = useState([]);
  const [accordionActiveKey, setAccordionActiveKey] = useState(null);
  const [isFilterActive, setIsFilterActive] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [loading, setLoading] = useState(true);
  const [productLoading, setProductLoading] = useState(true);
  const [brandOptions, setBrandOptions] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [isStockSelected, setIsStockSelected] = useState(false);
  const [priceRange, setPriceRange] = useState({ from: "", to: "" });
  const [sortOption, setSortOption] = useState("");

  useEffect(() => {
    document.body.classList.add("hide-header");

    return () => {
      document.body.classList.remove("hide-header");
    };
  }, []);

  const fetchCategories = async () => {
    setLoading(true);
    const apiUrl = `${API_BASE_URL}/category`;
    try {
      const response = await fetch(apiUrl);
      if (response !== undefined && response?.status === 200) {
        const result = await response.json();
        setCategories(result.data.menu);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [paramCategoryName, paramSubCategoryName, paramLastSubCategoryName]);

  const handleSelectedSubCategory = () => {
    const selectedSubCategories =
      categories?.find((cat) => cat?.id === selectedCategoryId)?.sub || [];
    setSubCategories(selectedSubCategories);
  };

  useEffect(() => {
    if (!!selectedCategoryId) handleSelectedSubCategory();
  }, [selectedCategoryId]);

  useEffect(() => {
    if (!!paramCategoryName && !!categories?.length) {
      const filterCategory = categories.find(
        (cat) => cat?.slug === paramCategoryName
      );
      setSelectedCategoryId(filterCategory?.id);
    }
  }, [paramCategoryName, categories]);

  // Improved fetchProducts function
  const fetchProducts = async (filters = {}) => {
    setProductLoading(true); // Show loading indicator
    const apiUrl = `${API_BASE_URL}/products`;

    const requestBody = {
      category_slug: paramCategoryName,
      ...filters
    };

    // Add sort option if selected
    if (sortOption) {
      requestBody.sort = sortOption;
    }

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody),
      });
      if (response !== undefined && response?.status === 200) {
        const result = await response.json();
        
        if (result.data && result.data.product_list) {
          // Create a new array to ensure state update
          const newProducts = [...result.data.product_list];
          setProducts(newProducts);
          
          // If this is the first fetch, extract and set brands
          if (!filters.brands && result.data.product_list.length > 0) {
            const uniqueBrands = extractUniqueBrands(result.data.product_list);
            setBrandOptions(uniqueBrands);
          }
        } else {
          // Handle empty product list
          setProducts([]);
        }
      }
    } catch (error) {
      setProducts([]); // Reset products on error
    } finally {
      setProductLoading(false); // Hide loading indicator
    }
  };

  const extractUniqueBrands = (products) => {
    const brandMap = new Map();
    const uniqueBrands = [];
    
    products.forEach(product => {
      if (product.brand && !brandMap.has(product.brand)) {
        brandMap.set(product.brand, true);
        uniqueBrands.push({
          name: product.brand,
          id: product.brand
        });
      }
    });
    
    return uniqueBrands;
  };

  useEffect(() => {
    if (
      !!paramCategoryName ||
      !!paramSubCategoryName ||
      !!paramLastSubCategoryName
    ) {
      setSelectedBrands([]);
      setIsStockSelected(false);
      setPriceRange({ from: "", to: "" });
      setSortOption("");
      priceRangeFilterReset(paramCategoryName);
      
      // Clear brands list immediately
      setBrandOptions([]);
      
      // Then fetch new products
      fetchProducts();
    }
  }, [paramCategoryName, paramSubCategoryName, paramLastSubCategoryName]);

  const onBrandSelect = (selectedList, selectedItem) => {
    setSelectedBrands(prevBrands => {
      const newBrands = [...prevBrands, selectedItem.id];
      return newBrands;
    });
  };

  const onBrandRemove = (selectedList, removedItem) => {
    setSelectedBrands(prevBrands => {
      const updatedBrands = prevBrands.filter(brand => brand !== removedItem.id);
      return updatedBrands;
    });
  };

  // Handle stock filter
  const onStockSelect = (selectedList, selectedItem) => {
    setIsStockSelected(selectedItem !== null && selectedItem !== undefined);
  };

  // Handle price range filter
  const handlePriceRangeChange = (name, value) => {
    setPriceRange(prev => ({ ...prev, [name]: value }));
  };

  // Handle sort change
  const handleSortChange = (e) => {
    setSortOption(e.target.value);
  };

  const applyFilters = (filterType) => {
    
    const filters = {};
    
    // Always apply brand filters if brands are selected
    if ((!filterType || filterType === "brand") && selectedBrands.length > 0) {
      filters.brands = selectedBrands.map(brandId => ({ brand: brandId }));
    }
    
    // Apply stock filter if selected
    if ((!filterType || filterType === "stock") && isStockSelected) {
      filters.allow_purchase = true;
    }
  
    if ((!filterType || filterType === "price") && (priceRange.from || priceRange.to)) {
      const min = priceRange.from ? parseInt(priceRange.from) : 0;
      const max = priceRange.to ? parseInt(priceRange.to) : 999999;
    
      if (min <= max) {
        filters.price = {
          min: min,
          max: max
        };
      } else {
        // Show error or correct values
        console.error("Invalid price range: min > max");
      }
    }
    
    // Apply sort
    if (sortOption) {
      filters.sort = sortOption;
    }
    
    setProducts(filters)

    
    // Fetch products with applied filters
    // fetchProducts(filters);
  };

  // Fixed resetFilters function
  const resetFilters = () => {
    // Reset all filter states
    setSelectedBrands([]);
    setIsStockSelected(false);
    setPriceRange({ from: "", to: "" });
    setSortOption("");
    
    // Fetch products without filters
    fetchProducts();
  };

  const activeAccordionKey = React.useMemo(() => {
    if (!subCategories.length) return null;

    if (
      !!paramCategoryName &&
      !paramSubCategoryName &&
      !paramLastSubCategoryName
    ) {
      return String(subCategories[0]?.id);
    }

    const found = subCategories.find(
      (subcat) => subcat?.slug === paramSubCategoryName
    );

    return found ? String(found.id) : null;
  }, [
    paramCategoryName,
    paramSubCategoryName,
    paramLastSubCategoryName,
    subCategories,
  ]);

  useEffect(() => {
    setAccordionActiveKey(activeAccordionKey);
  }, [activeAccordionKey]);

  const handleHeaderActions = (type) => {
    switch (type) {
      case "search":
        setIsSearchActive(!isSearchActive);
        setIsFilterActive(false);
        break;
      case "filter":
        setIsFilterActive(!isFilterActive);
        setIsSearchActive(false);
        break;
      default:
        break;
    }
  };

useEffect(() => {
  // If products are loaded and no products in current active accordion, find one with products
  if (!loading && products.length > 0 && subCategories.length > 0) {
    // Check if current accordionActiveKey has products
    const currentCategoryId = accordionActiveKey;
    const hasProductsInCategory = products.some(
      product => String(product.category_id) === currentCategoryId
    );
    
    if (!hasProductsInCategory) {
      // Find first category with products
      const firstCategoryWithProducts = subCategories.find(subcat => 
        products.some(product => String(product.category_id) === String(subcat.id))
      );
      
      if (firstCategoryWithProducts) {
        setAccordionActiveKey(String(firstCategoryWithProducts.id));
      }
    }
  }
}, [products, loading, subCategories]);

  return (
    <>
      <div className="category-header mobile">
        <h6 className="m-0 text-truncate">All Categories</h6>
        <div className="button-group pl-3 ml-auto">
          <button
            className={`button ${(isFilterActive && "active") || ""}`}
            onClick={() => handleHeaderActions("filter")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="16"
              viewBox="0 0 18 16"
              fill="none"
            >
              <path
                d="M17 1H1L7.4 8.35778V13.4444L10.6 15V8.35778L17 1Z"
                stroke="#131921"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          <button
            className={`button ${(isSearchActive && "active") || ""}`}
            onClick={() => handleHeaderActions("search")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M10.0758 15.8333C13.7577 15.8333 16.7425 12.8486 16.7425 9.16667C16.7425 5.48477 13.7577 2.5 10.0758 2.5C6.39395 2.5 3.40918 5.48477 3.40918 9.16667C3.40918 12.8486 6.39395 15.8333 10.0758 15.8333Z"
                stroke="#131921"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M18.4092 17.5L14.7842 13.875"
                stroke="#131921"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
        {(isFilterActive && <div className="filter-backdrop"></div>) || null}
        <div className={`filter ${(isFilterActive && "visible") || ""}`}>
          <MobileProductFilterSection
            isFilterActive={isFilterActive}
            setIsFilterActive={setIsFilterActive}
            brandOptions={brandOptions}
            selectedBrands={selectedBrands}
            onBrandSelect={onBrandSelect}
            onBrandRemove={onBrandRemove}
            isStockSelected={isStockSelected}
            onStockSelect={onStockSelect}
            priceRange={priceRange}
            handlePriceRangeChange={handlePriceRangeChange}
            applyFilters={applyFilters}
            resetFilters={resetFilters}
            handleSortChange={handleSortChange}
          />
        </div>

        <div className={`search ${(isSearchActive && "visible") || ""}`}>
          <ProductSearchAutocomplete />
        </div>
      </div>
      <div className="category-main mobile">
        <MobileProductListSidebar
          categories={categories}
          selectedCategoryId={selectedCategoryId}
        />
        <MobileProductListListing
          products={products}
          subCategories={subCategories}
          accordionActiveKey={accordionActiveKey}
          setAccordionActiveKey={setAccordionActiveKey}
          isLoading={productLoading}
        />
      </div>
    </>
  );
};

const actionCreators = {
  brandFilter: productsListByBrand,
  allowPurchaseFilter: productsListByAllowPurchase,
  priceRangeFilter: productsListByPriceRange,
  priceFilterByType: priceFilterByType,
  priceRangeFilterReset: resetFilterByPriceRange,
};

export const MobileProductListMain = connect(
  null,
  actionCreators
)(MobileProductListMainComponent);