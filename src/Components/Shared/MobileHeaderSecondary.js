import React, { useState } from "react";
import ProductSearchAutocomplete from "../Header/ProductSearchAutocomplete";
import { useHistory } from "react-router-dom";

export const MobileHeaderSecondary = ({
  title,
  hasBack = true,
  hasSearch = true,
  link,
}) => {
  const history = useHistory();

  const [isSearchActive, setIsSearchActive] = useState(false);

  const handleHeaderActions = (type) => {
    switch (type) {
      case "search":
        setIsSearchActive(!isSearchActive);
        break;
      default:
        break;
    }
  };

  return (
    <div className="header header--mobile header--secondary">
      {(hasBack && (
        <button
          className={`button mr-3`}
          onClick={() => (!!link ? history.push(link) : history.goBack())}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="8"
            height="14"
            viewBox="0 0 8 14"
            fill="none"
          >
            <path
              d="M7 13L1 7L7 1"
              stroke="black"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      )) ||
        null}
      <h6 className="m-0  text-truncate">{title}</h6>
      {(hasSearch && (
        <>
          <button
            className={`button ml-auto ${(isSearchActive && "active") || ""}`}
            onClick={() => handleHeaderActions("search")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M10.0758 15.8333C13.7577 15.8333 16.7425 12.8486 16.7425 9.16667C16.7425 5.48477 13.7577 2.5 10.0758 2.5C6.39395 2.5 3.40918 5.48477 3.40918 9.16667C3.40918 12.8486 6.39395 15.8333 10.0758 15.8333Z"
                stroke="#131921"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M18.4092 17.5L14.7842 13.875"
                stroke="#131921"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <div className={`search ${(isSearchActive && "visible") || ""}`}>
            <ProductSearchAutocomplete />
          </div>
        </>
      )) ||
        null}
    </div>
  );
};
