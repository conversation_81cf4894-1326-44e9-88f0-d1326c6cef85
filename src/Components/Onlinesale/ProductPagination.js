import React from 'react';
import PropTypes from 'prop-types';
import { Link } from "react-router-dom";

const propTypes = {
    totalItems: PropTypes.number.isRequired,
    onPageChange: PropTypes.func.isRequired,
    initialPage: PropTypes.number,
    pageSize: PropTypes.number,
    currentPage: PropTypes.number
}

const defaultProps = {
    initialPage: 1,
    pageSize: 21
}

class Pagination extends React.Component {
    constructor(props) {
        super(props);
        this.state = { 
            pager: {} 
        };
    }

    componentDidMount() {
        // Calculate pager on mount
        this.updatePager();
    }

    componentDidUpdate(prevProps) {
        // Only update pager if totalItems or currentPage changes
        if (prevProps.totalItems !== this.props.totalItems || 
            prevProps.currentPage !== this.props.currentPage) {
            this.updatePager();
        }
    }

    updatePager() {
        const { totalItems, currentPage, pageSize } = this.props;
        // Calculate pager without triggering page change
        const pager = this.getPager(totalItems, currentPage, pageSize);
        this.setState({ pager });
    }

    setPage(page) {
        // Don't do anything if clicking the current page
        if (page === this.props.currentPage) {
            return;
        }
        
        // Call change page function in parent component
        this.props.onPageChange(page);
    }

    getPager(totalItems, currentPage, pageSize) {
        // Default to first page
        currentPage = currentPage || 1;

        // Default page size
        pageSize = pageSize || 21;

        // Calculate total pages
        const totalPages = Math.ceil(totalItems / pageSize);

        let startPage, endPage;
        if (totalPages <= 10) {
            // Less than 10 total pages so show all
            startPage = 1;
            endPage = totalPages;
        } else {
            // More than 10 total pages so calculate start and end pages
            if (currentPage <= 6) {
                startPage = 1;
                endPage = 10;
            } else if (currentPage + 4 >= totalPages) {
                startPage = totalPages - 9;
                endPage = totalPages;
            } else {
                startPage = currentPage - 5;
                endPage = currentPage + 4;
            }
        }

        // Create an array of pages to ng-repeat in the pager control
        const pages = [...Array((endPage + 1) - startPage).keys()].map(i => startPage + i);

        // Return object with all pager properties required by the view
        return {
            totalItems,
            currentPage,
            pageSize,
            totalPages,
            startPage,
            endPage,
            pages
        };
    }

    render() {
        const { pager } = this.state;

        if (!pager.pages || pager.pages.length <= 1) {
            // Don't display pager if there is only 1 page
            return null;
        }

        return (
            <ul className="pagination">
                <li className={pager.currentPage === 1 ? 'disabled' : ''}>
                    <Link to="#" onClick={() => this.setPage(1)}>First</Link>
                </li>
                <li className={pager.currentPage === 1 ? 'disabled' : ''}>
                    <Link to="#" onClick={() => this.setPage(pager.currentPage - 1)}>Previous</Link>
                </li>
                {pager.pages.map((page, index) =>
                    <li key={index} className={pager.currentPage === page ? 'active' : ''}>
                        <Link to="#" onClick={() => this.setPage(page)}>{page}</Link>
                    </li>
                )}
                <li className={pager.currentPage === pager.totalPages ? 'disabled' : ''}>
                    <Link to="#" onClick={() => this.setPage(pager.currentPage + 1)}>Next</Link>
                </li>
                <li className={pager.currentPage === pager.totalPages ? 'disabled' : ''}>
                    <Link to="#" onClick={() => this.setPage(pager.totalPages)}>Last</Link>
                </li>
            </ul>
        );
    }
}

Pagination.propTypes = propTypes;
Pagination.defaultProps = defaultProps;

export default Pagination;
