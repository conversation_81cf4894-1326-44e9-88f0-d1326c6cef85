import React, {Component} from 'react';
import PartnersSlider from "react-slick";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import Skeleton from "react-loading-skeleton";
import { Link } from "react-router-dom";
import API_BASE_URL from '../../config/api';


function chunkArray(arr, size) {
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
    }
    return result;
}
export default class PartnersListSection extends Component {
    constructor(props) {
        super(props);
        this.state = {
            logoerror: null,
            customerlogos: [],
            topProducts: [],
            isTopProductLoaded: false,
        }
    }

    componentDidMount() {
        //const apiUrl = 'https://www.evisionstore.com/api/home/<USER>';
        const apiUrl = `${API_BASE_URL}/specialproducts`;

        fetch(apiUrl)
        .then(res => res.json())
        .then(
            (result) => {
                this.setState({
                    topProducts: result.data,
                    isTopProductLoaded: true,
                    //customerlogos: result.home_brands
                });
            },
            (error) => {
                this.setState({ logoerror: error });
            }
        )
    }

    // Add this method to handle product redirection
    handleProductLink(product) {
        let productModel = product.modelo;
        if (productModel) {
            let checkSlash = "/";
            if (productModel.indexOf(checkSlash) !== -1) {
                productModel = product.modeloch;
            }
            return `/producto/${productModel}`;
        }
        return '#';
    }

    render(){
        const { logoerror, customerlogos, topProducts, isTopProductLoaded } = this.state;
        const productRows = chunkArray(topProducts, 4);
        const partnersslidersettings = {
            infinite: true,
            autoplay: true,
            speed: 600,
            slidesToShow: 8,
            slidesToScroll: 1,
            dots: false,
            responsive: [
                {
                  breakpoint: 480,
                  settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                    dots: false,
                    autoplay: true
                  }
                }
            ]
        };

        return(
            <section className="partner-container">
                <div className="container">
                <div className="row">
                    <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div className="product-title common-section-title">
                            <h2>Tiendas de marcas</h2>
                        </div>
                    </div>
                    <section className="customer-logos">
                        <PartnersSlider {...partnersslidersettings}>
                            <div key='hisense'>
                                <Link to={`/brandshop/hisense`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-11.png" alt="Hisense"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='lg'>
                                <Link to={`/brandshop/lg`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-7.png" alt="LG"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='samsung'>
                                <Link to={`/brandshop/samsung`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-4.png" alt="Samsung"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='oster'>
                                <Link to={`/brandshop/oster`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-8.png" alt="Oster"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='sankey'>
                                <Link to={`/brandshop/sankey`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-6.png" alt="Sankey"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='drija'>
                                <Link to={`/brandshop/drija`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-12.png" alt="Drija"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='canon'>
                                <Link to={`/brandshop/canon`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-1.png" alt="Canon"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='frigidaire'>
                                <Link to={`/brandshop/frigidaire`} >
                                    <picture>
                                        <img src="/images/marcalogo/marca_logo-13.png" className="frigidaire-img" alt="Frigidaire"/>
                                    </picture>
                                </Link>
                            </div>
                            <div key='tcl'>
                                <Link to={`/brandshop/tcl`} >
                                    <picture>
                                        <img src="/images/marcalogo/tcl.png" alt="TCL"/>
                                    </picture>
                                </Link>
                            </div>
                            {/* {customerlogos.map(customerlogos => (
                                <div key={customerlogos.brand_id}>
                                    <Link to={`/brandshop/${customerlogos.brand_name.toString().toLowerCase()}`} ><picture><img src={customerlogos.brand_image} alt={customerlogos.brand_name}/></picture></Link>
                                </div>
                            ))} */}
                        </PartnersSlider>
                    </section>
                    <section className="trend-product-section">
                        <div className="container">
                            <div class="product-title common-section-title"><h2>Tendencia Productos</h2></div>
                            
                            {!isTopProductLoaded ? (
                                Array.from({length: 8}).map((item, index) => (
                                    <div key={index} className="col-md-3 mb-30">
                                        <div className="trend-product-section__box">
                                            <Skeleton height={100} width={100} />
                                            <Skeleton height={15} width={100} />
                                            <Skeleton height={15} width={100} />
                                            <Skeleton height={15} width={100} />
                                        </div>
                                    </div>
                                ))
                            ) : (
                                productRows.map((row, rowIndex) => (
                                    <div className={`row ${rowIndex > 0 ? "mt-3" : ""}`} key={rowIndex}>
                                    {row.map((product) => (
                                        <div className="col-md-3 mb-30" key={product.id}>
                                        <Link to={this.handleProductLink(product)} className="trend-product-section__box">
                                            <div className="trend-product-section__box-img">
                                            <img src={product.product_image} alt={product.product_name} />
                                            <span className="trend-product-section__wishlist">
                                                {/* <img src="/images/heart.svg" alt="Heart" /> */}
                                            </span>
                                            </div>
                                            <h3>{product.product_name}</h3>
                                            <div className="trend-product-section__box-footer">
                                            <h4>${product.price}</h4>
                                            <img src="images/new-images/icon/top-cart.svg" alt="Cart" />
                                            </div>
                                        </Link>
                                        </div>
                                    ))}
                                    </div>
                                ))
                            )}
                        </div>
                    </section>
                </div>
                </div>
            </section>
        )
    }
}