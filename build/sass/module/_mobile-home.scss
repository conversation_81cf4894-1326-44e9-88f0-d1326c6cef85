.recomended--mobile {
    .recomended-botttom {
        padding-top: 30px !important;
        padding-bottom: 15px !important;
        background: #ECF2F8 !important;
    }
    .recomended-botttom-slider {
        margin-top: 0;
    }
    .slick-list {
        padding: 0 43px;
        margin-left: -45px !important;
    }
    .product-title {
        display: flex;
        justify-content: space-between !important;
        margin-bottom: 10px;
        h2 {
            font-size: 16px;
            line-height: 20px;
            font-weight: 500;
            color: #131921;
        }
        .view-all {
            text-decoration: underline;
            font-size: 13px;
            font-weight: 400;
            color: #131921;
        }
    }
    .recomended-slide {
        border-radius: 10px;
        background: #fff;
        padding: 5px;
        min-height: 125px;
        margin-bottom: 13px;
        position: relative;
        border-radius: 15px;
        border: 1px solid rgba(45, 91, 165, 0.3);
        padding: 5px 5px 10px;
        width: 94%;
        .deal-link-wish {
            position: absolute;
            top: 10px;
            left: 15px;
            z-index: 9;
            img {
                width: 13px;
            }
        }
        .figure-area {
            background: #E0E6F2;
            border-radius: 10px;
            background: #E0E6F2;
            padding: 5px;
            min-height: 125px;
            margin-bottom: 13px;
        }
    }
    .product-rating {
        display: flex;
        align-items: center;
        color: #3C525D;
        font-size: 11px;
        line-height: 13px;
        margin-bottom: 10px;
        img {
            width: 11px;
        }
        .product-rating-star {
            margin-right: 5px;
            position: relative;
            top: -2px;
        }
        .product-rating-number {
            font-size: 11px;
            line-height: 13px;
            font-weight: 400;
            color: #3C525D;
            margin-right: 5px;
            display: inline-block;
        }
        .product-review {
            font-size: 11px;
            line-height: 13px;
            font-weight: 400;
            color: #3C525D;
            margin-left: 5px;
            display: inline-block;
        }
    }
}

.deal-container--mobile.for-mobile {
    background: #ECF2F8 !important;
    padding-bottom: 30px;
    .product-title {
        justify-content: space-between !important;
        margin-bottom: 20px;
        h2 {
            font-size: 16px;
            line-height: 20px;
            font-weight: 500;
            color: #131921;
        }
        .view-all {
            text-decoration: underline;
            font-size: 13px;
            font-weight: 400;
            color: #131921;
        }
    }
    .deal-link-item {
        position: relative;
        border-radius: 15px;
        border: 1px solid rgba(45, 91, 165, 0.3);
        padding: 5px 5px 10px;
        width: 94%;
        figure {
            border-radius: 10px;
            background: #E0E6F2;
            padding: 5px;
            min-height: 125px;
            margin-bottom: 13px;
        }
        .deal-link-wish {
            position: absolute;
            top: 10px;
            left: 15px;
            z-index: 9;
            img {
                width: 13px;
            }
        }
    }
    .product-rating {
        display: flex;
        align-items: center;
        color: #3C525D;
        font-size: 11px;
        line-height: 13px;
        margin-bottom: 10px;
        img {
            width: 11px;
        }
        .product-rating-star {
            margin-right: 5px;
            position: relative;
            top: -2px;
        }
        .product-rating-number {
            font-size: 11px;
            line-height: 13px;
            font-weight: 400;
            color: #3C525D;
            margin-right: 5px;
            display: inline-block;
        }
        .product-review {
            font-size: 11px;
            line-height: 13px;
            font-weight: 400;
            color: #3C525D;
            margin-left: 5px;
            display: inline-block;
        }
    }
    .pricecart {
        a {
            img {
                width: 14px !important;
            }
        }
    }
    .heading-wrap {
        min-height: 80px;
    }
    .slick-list {
        padding: 0 43px;
        margin-left: -25px !important;
    }
}

.category-container--mobile {
    margin-bottom: 20px;
    .department-link-item {
        figure {
            border: none;
            height: auto;
            max-width: none;
            background: none;
            margin-bottom: 5px;
            img {
                width: 30px;
            }
        }
    }
    .department-heading-wrap {
        width: 70px;
        h3 {
            font-size: 13px;
            line-height: 13px;
            font-weight: 400;
            color: #131921;
        }
    }
}

.section-top-tag {
    .slick-slide {
        // width: auto !important;
    }
}

.partner-container--mobile {
    background: #ECF2F8;
    .product-title {
        width: 100%;
        display: flex;
        justify-content: space-between !important;
        margin-bottom: 10px;
        &:after,
        &:before {
            display: none;
        }
        h2 {
            font-size: 16px;
            line-height: 20px;
            font-weight: 500;
            color: #131921;
            background: none;
        }
        .view-all {
            text-decoration: underline;
            font-size: 13px;
            font-weight: 400;
            color: #131921;
        }
    }
    .customer-logos {
        background: #fff;
        border-radius: 30px;
        border: 1px solid rgba(45, 91, 165, 0.3);
        padding: 13px 0;
    }
    .slick-slide{
        img{
            @media (max-width: 767px) {
                -webkit-filter: grayscale(100%);
                filter: grayscale(100%);
            }
        }
    }
}

.contact-info-container--mobile {
    background: #ECF2F8;
    .callinfo {
        padding: 12px 30px;
    }
    .mobile-info {
        padding: 12px 30px;
        a {
            padding: 0;
        }
        .callinfo {
            padding: 0;
        }
    }
}

.offer-container--mobile {
    background: #ECF2F8;
}