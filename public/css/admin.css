html, *{
	color: #999;
	font-family: helvetica, arial, sans-serif;
}


body{
	max-width: 600px;
	margin: 0 auto;
	padding: 0 1em;
}

legend{
	font-weight: bold;
	font-size: 1.15em;
}

fieldset{
	border: 0;
	margin: 0;
	padding: 0
}

label{
	display: none;
}

input[type="text"], input[type="password"]{
	padding: 1em;
	border: 1px solid #CCC;
	width: 95%;
}

input[type="text"]{
	border-radius: 1em 1em 0 0;
}

input[type="password"]{
	margin-top: -2px;
	border-radius: 0 0 1em 1em;
}

button, footer a{
	width: 100%;
	padding: .5em;
	border-radius: .5em;
	border: 1px solid;
	background-color: #5B74A6;
	color: #FFF;
	font-size: 1.15em;
	font-weight: bold;
	cursor: pointer;
}

header h1{
	display: block;
	height: 74px;
	background: url("../images/logo_evision-notrans.png") top center no-repeat;
}

header h1 a{
	visibility: hidden;
}

footer {
	text-align: center;
	font-size: .75em
}

footer a{
	display: block;
	background-color: #69A74E;
	width: 97%;
	text-decoration: none;
	font-size: 1.5em;
}