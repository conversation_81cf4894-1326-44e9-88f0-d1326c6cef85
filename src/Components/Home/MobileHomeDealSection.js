import React, {Component} from 'react';
import DealSlider from "react-slick";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import { Link } from "react-router-dom";
import API_BASE_URL from '../../config/api';
import { connect } from "react-redux";
import { addToCart, addToCart2 } from "../../Services/Actions/cart.actions";

function SampleNextArrow(props) {
    const { className, style, onClick } = props;
    return (
        <div
        className={className}
        style={{ ...style, display: "block", background: "red" }}
        onClick={onClick}
        />
    );
}

function SamplePrevArrow(props) {
    const { className, style, onClick } = props;
    return (
        <div
        className={className}
        style={{ ...style, display: "block", background: "red" }}
        onClick={onClick}
        />
    );
}

class MobileHomeDealSectionProp extends Component {

    constructor(props) {
        super(props);
        this.state = {
            error: null,
            topProducts: [],
            addToCartButtonnLoader: "hidden",
            addToCartButtonnCart: "",
            addToCartButton: true,
            addToCartButtonDisabled: false,
            cartErrorMessage: "",
            clickedProductId: "",
            isCartAddError: false,
            showCartReturnModal: false,
            showUpdateCartMsgModal: false
        }
        this.openCartReturnPop = this.openCartReturnPop.bind(this);
        this.closeCartReturnPop = this.closeCartReturnPop.bind(this);
        this.cartUpdatePopupResOpen = this.cartUpdatePopupResOpen.bind(this);
        this.cartUpdatePopupResClose = this.cartUpdatePopupResClose.bind(this);
    }

    componentDidMount() {
        //const apiUrl = 'https://www.evisionstore.com/api/home/<USER>';
        const apiUrl = `${API_BASE_URL}/specialproducts`;

        fetch(apiUrl)
        .then(res => res.json())
        .then(
            (result) => {
                //console.log(result)
                this.setState({
                    topProducts: result.data
                });
            },
            (error) => {
                this.setState({ error });
            }
        )
    }

    openCartReturnPop() {
        this.setState({ showCartReturnModal: true });
    }
    
    closeCartReturnPop() {
        this.setState({ showCartReturnModal: false });
    }
    
    cartUpdatePopupResOpen() {
        this.setState({ showUpdateCartMsgModal: true });
    }
    
    cartUpdatePopupResClose() {
        this.setState({ showUpdateCartMsgModal: false });
    }

    handleOnAddToCart(product_id, product_name, product_image, price, quantity, brand, modelo, category_name, is_allowed_bac_credomatic) {
            this.setState({
                addToCartButtonnLoader: "",
                addToCartButtonnCart: "hidden",
                addToCartButton: true,
                addToCartButtonDisabled: true,
                cartErrorMessage: "",
                clickedProductId: product_id,
            });
            let checked_variation_product_id = 0;
            this.props.addToCart(product_id, product_name, product_image, price, quantity, checked_variation_product_id, modelo, is_allowed_bac_credomatic);
    
            setTimeout(
                () =>
                    this.setState(
                        {
                            isCartAddError: this.props.isCartAddError,
                        },
                        () => {
                            if (this.state.isCartAddError) {
                                this.setState({
                                    cartErrorMessage: this.props.cartErrorMessage,
                                    addToCartButtonnLoader: "hidden",
                                    addToCartButtonnCart: "",
                                    addToCartButton: true,
                                    addToCartButtonDisabled: false,
                                    clickedProductId: "",
                                });
                                this.cartUpdatePopupResOpen();
                                setTimeout(() => this.cartUpdatePopupResClose(), 5000);
                            } else {
                                this.openCartReturnPop();
                                this.setState({
                                    addToCartButtonnLoader: "hidden",
                                    addToCartButtonnCart: "",
                                    addToCartButton: false,
                                    addToCartButtonDisabled: false,
                                    cartErrorMessage: "",
                                });
    
                                window.fbq("track", "AddToCart", {
                                    content_ids: [product_id],
                                    content_type: "product",
                                    value: price,
                                    currency: "USD",
                                });
    
                                window.ga("ec:addProduct", {
                                    id: modelo,
                                    name: brand + " " + modelo,
                                    price: price,
                                    brand: brand,
                                    category: category_name,
                                    quantity: 1,
                                });
                                window.ga("ec:setAction", "add");
                                window.ga("send", "event", "Product List Page", "click", "addToCart");
    
                                window.gtag("event", "add_to_cart", {
                                    send_to: "UA-521381-2/yuuFCLjX-aUBENbiovUC",
                                    value: price,
                                    currency: "USD",
                                    event_callback: true,
                                });
                            }
                        }
                    ),
                3000
            );
        }
    

    render(){
        const mobiledelalslidersettings = {
            //dots: true,
            infinite: true,
            autoplay: true,
            speed: 500,
            //rows: 1,
            //slidesPerRow: 2,
            slidesToShow: 2,
            slidesToScroll: 1,
            nextArrow: <SampleNextArrow />,
            prevArrow: <SamplePrevArrow />,
            responsive: [
                {
                  breakpoint: 767,
                  settings: {
                    slidesToShow: 2,
                    centerMode: true,
                    centerPadding: '43px'
                  }
                }
            ]
        };

        const { topProducts } = this.state;

        return(
            <section className="deal-container deal-container--mobile for-mobile">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div className="product-title">
                                <h2>Productos de tendencia {/*<span>Trato especial</span>*/}</h2>
                                {/* <Link className="view-all">View All</Link> */}
                            </div>
                        </div>
                        <section className="customer-logos slider">
                            <DealSlider {...mobiledelalslidersettings}>
                            
                            {topProducts.length !== 0 ? (
                                topProducts.map((products, index) => (
                                    <div className="slide" key={index}>
                                        <div className="deal-link-item">
                                            {/* <div className="deal-link-wish">
                                                <img src={`${process.env.PUBLIC_URL}/images/new-images/icon/heart.svg`} alt="Heart" />
                                            </div> */}
                                            <Link to={`/producto/${products.modelo}`}>
                                                <figure>
                                                    <a href="javascript:void(0)" className="wishlist-link">
                                                        <img src={`${process.env.PUBLIC_URL}/images/new-images/icon/wishlist-icon.svg`} width="20" height="18" alt="wishlist" />
                                                    </a>
                                                    <img src={products.product_image} alt={products.product_name} />
                                                </figure>
                                                <div className="heading-wrap">
                                                    <h2>{products.product_name}</h2>
                                                    {/* <div className="review"><img src="/images/new-images/icon/star.svg" width="20" height="19" title="star" alt="star" /><span>5.0 | 14 opiniones</span></div> */}
                                                    <div className="product-rating">
                                                        <div className="product-rating-star">
                                                            <img src={`${process.env.PUBLIC_URL}/images/new-images/icon/star.svg`} alt="Star" />
                                                        </div>
                                                        <span className="product-rating-number">{products.product_rating}</span> | <span className="product-review">{products.review_count}</span>
                                                    </div>
                                                    <div className="pricecart">
                                                    {products.check_spcl_prc === "yes" ? (
                                                        <div className="price">$ {products.special_price} - <span className="oldprice">$ {products.price}</span></div>
                                                    ) : (
                                                        <div className="price">$ {products.price}</div>
                                                    )}
                                                  <button
                                                    onClick={(e) => {
                                                                            e.preventDefault();
                                                                            this.handleOnAddToCart(
                                                                                products.product_id,
                                                                                products.product_name,
                                                                                products.product_image,
                                                                                products.price,
                                                                                "1",
                                                                                products.brand,
                                                                                products.modelo,
                                                                                products.category_name,
                                                                                products.is_allowed_bac_credomatic
                                                                            )
                                                                        }

                                                                        }
                                                    disabled={this.state.addToCartButtonDisabled}>
                                                        <img
                                                            src={`${process.env.PUBLIC_URL}/images/new-images/icon/top-cart.svg`}
                                                            width="20"
                                                            height="16"
                                                            alt="Cart"
                                                        />
                                                    </button>
                                                    {/* <div className="price">$ {products.price}</div> */}
                                                    
                                                    {/* <h3>Ends in {products.time_ends}</h3> */}
                                                    </div>
                                                </div>
                                            </Link>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <></>
                            )}

                            </DealSlider>
                        </section>
                    </div>
                </div>
            </section>
        )
    }
}

function mapStateToProps(state) {
    localStorage.setItem("cartData", JSON.stringify(state.cart.cartData));
    return {
        isLoggedIn: state.user.isLoggedIn,
        loggedInUserEmail: typeof state.user.user === "undefined" ? "" : state.user.user.email,
        isError: state.user.isError,
        errorMessage: state.user.errorMessage,
        cartData: state.cart.cartData,
        filteredCategoryProducts: state.filter.filteredProducts,
        isFilterProductsLoaded: state.filter.isFilterProductsLoaded,
        isCartAddError: state.cart.isCartAddError,
        cartErrorMessage: state.cart.cartErrorMessage,
    };
}

const MapDispachToProps = (dispatch) => ({
    addToCart: () => dispatch(addToCart()),
});

const actionCreators = {
    addToCart: addToCart2,
    //resetFilterByCategorySlug: resetFilterByCategorySlug,
    //setBrandOptions: setBrandOptions,
    //setProductsById: setProductsById,
};

const MobileHomeDealSection = connect(mapStateToProps, actionCreators)(MobileHomeDealSectionProp);
export default MobileHomeDealSection;