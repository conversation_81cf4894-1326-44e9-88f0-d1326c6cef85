.section-title-inner {
  font-size: 35px;
  line-height: 27px;
  color: #3C525D;
  font-weight: 600;
  margin: 30px 0 50px;
  @media (max-width: 1024px) {
    font-size: 24px;
  }
}

.cart-list{
  position: relative;
  width: 100%;
  display: inline-block;
}
.cart-item{
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px;
  @media (max-width: 1024px) {
    align-items: center;
    justify-content: center;
  }
}
.cart-thumb_image{
  width: 180px;
  height: 180px;
  border-radius: 9px;
  background-color: #dfe6f1;
  padding: 10px;
  margin-right: 30px;
  @media (max-width: 1024px) {
    width: 120px;
    height: 120px;
    margin-right: 15px;
  }
  @media (max-width: 767px) {
    width: 140px;
    height: 140px;
  }

}

.cart-product_description{
  width: 65%;
  @media (max-width: 1024px) {
    width: auto;
    flex: 1;
  }
  @media (max-width: 767px) {
    width: 55%;
  }
  .cart-product_info{
    padding-bottom: 17px;
    border-bottom: 1px solid #CFD7E5;
    @media (max-width: 1024px) {
      padding-bottom: 10px;
    }
    @media (max-width: 767px) {
      padding-bottom: 6px;
    }
  }
  .CartDescription{
    h4{
      font-size: 24px;
      line-height: 27px;
      color: #3C525D;
      font-weight: 600;
      margin: 0;
      @media (max-width: 1024px) {
        font-size: 18px;
      }
      @media (max-width: 767px) {
        font-size: 16px;
      }
      a{
        color: #3C525D !important;
        text-decoration: none !important;
      }
    }
    p{
      font-size: 14px;
      line-height: 27px;
      color: #3C525D;
      margin: 0;
      @media (max-width: 1024px) {
        line-height: normal;
      }
      @media (max-width: 767px) {
        font-size: 12px;
      }
    }
  }
  .modprice{
    display: block;
    font-size: 30px;
    line-height: 27px;
    font-weight: 600;
    color: #3C525D;
    margin: 20px 0 0;
    @media (max-width: 1024px) {
      font-size: 20px;
      margin: 10px 0 0;
    }
    @media (max-width: 767px) {
      font-size: 18px;
      margin: 6px 0 0;
    }
  }
  .cart-product_action{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 17px;
    @media (max-width: 767px) {
      padding-top: 10px;
    }
    .input-group{
        border: 1px solid #CFD7E5;
        border-radius: 30px;
        padding: 2px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        @media (max-width: 767px) {
          padding: 0 8px;
        }
        .input-group-btn{
          float: none !important;
          width: auto !important;
          font-size: 20px !important;
          height: auto !important;
          .btn{
            color: #3C525D !important;
            margin:0;
            text-decoration: none;
            font-size: 16px;
          }
          
        }

        .form-control{
          flex: 0 0 50px;
          border: none;
          padding: 0;
          text-align: center;
          color: #3C525D;
          font-size: 12px;
          font-weight: 400;
        }
    }

    .action-area{
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      .action-area-buttton{
        margin: 0 15px;
        @media (max-width: 1024px) {
          margin: 0 10px;
        }
        @media (max-width: 767px) {
          margin: 0 8px;
        }
      }
    }

  }
}

.recipt-box{
  position: relative;
  background-color: #F0FAFF;
  border-radius: 16px;
  padding: 45px;
  margin-bottom: 30px;
  display: inline-block;
  width: 100%;
  @media (max-width: 767px) {
    margin: 30px 0 0;
    padding: 20px;
  }
  h4{
    color: #3C525D;
    font-size: 24px;
    line-height: 27px;
    font-weight: 600;
    margin-bottom: 17px;
    @media (max-width: 1024px) {
      font-size: 22px;
      line-height: normal;
    }
    @media (max-width: 767px) {
      font-size: 20px;
      text-align: center;
      margin: 0 0 30px;
    }
  }

  .recipt-row{
    position: relative;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    @media (max-width: 767px) {
      margin-bottom: 15px;
    }
    &:before{
      position: absolute;
      content: "-";
      width: 100%;
      height: 1px;
      border-top: 1px dashed #CDE3EE;
      left: 0;
      top: 50%;
    }
    span{
      position: relative;
      background-color: #F0FAFF;
      width: 33%;
      font-size: 14px;
      line-height: 27px;
      color: #3C525D;
      z-index: 1;
      &:last-child{
        width: 25%;
        text-align: right;
      }

      &.strong{
        font-weight: 700;
      }
    }
  }

  .btn-area{
    margin-top: 30px;
    display: block;
    .btn{
      background: #0075B9 !important;
      border-color: #0075B9 !important;
      border-radius: 50px;
      font-size: 16px !important;
      padding: 12px !important;
      font-weight: 500;
      img{
        width: 14px;
        margin-left: 10px;
      }
    }
    span{
      font-size: 14px;
      line-height: 27px;
      color: #3C525D;
      margin-top: 25px;
      display: inline-block;
      width: 100%;
      @media (max-width: 767px) {
        text-align: center;
        margin-top: 20px;
      }
    }
  }

  .coupon{
    width: 100%;
    display: block;
    position: relative;
    input{
      border: 1px solid #CFD7E5;
      background-color: #fff;
      border-radius: 50px;
      width: 100%;
      padding: 10px 75px 10px 17px;
      color: #3C525D;
      font-size: 14px;
      &::placeholder {
        color: #3C525D;
        opacity: 0.4; /* Firefox */
      }
      
      &::-ms-input-placeholder { /* Edge 12 -18 */
        color: #3C525D;
        opacity: 0.4;
      }
    }
    button{
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #0075B9;
      background: transparent;
      padding: 0;
    }
  }


}

.cartFooter {
  display: inline-block;
  width: 100%;
  margin-top: 30px;
  .box-footer{
    .btn{
      background: #0075B9 !important;
      border-color: #0075B9 !important;
      border-radius: 50px;
      font-size: 15px !important;
      padding: 10px 15px !important;
      font-weight: 500;
      color: #fff !important;
      img{
        width: 14px;
        margin-right: 10px;
      }
    }
  }
}
.cartFooterLatest {
  margin-top: 10px;
  @media (max-width: 767px) {
    margin-bottom: 20px;
  }
  .btn{
    background: #0075B9 !important;
    border-color: #0075B9 !important;
    padding: 10px 15px;
    border-radius: 50px;
    font-size: 16px !important;
    color: #fff !important;
    font-weight: 500;
    img{
      width: 11px;
      margin-right: 10px;
      
    }
  }
}
.costDetails{
  .col-lg-12{
    @media (max-width: 1024px) {
      padding: 0;
    }
  }
}