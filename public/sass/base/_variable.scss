// FONTS
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
$primary-font: "Poppins", sans-serif;
$secondary-font: '<PERSON>', sans-serif;

/*======Variable color start=======*/

$black:#000000;
$white:#ffffff;
$primary-color:#3C525D;
$slider-active: #0075B9;
$slider-active-lighten: rgba(0, 117, 185, 0.3);
$light-blue: #F0FAFF;
$separator-color: #D9D9D9;

$secondary-color: #444444;
$dark-font: #333333;
$light-grey: #969696;
$dark-grey: #5A5B5C;
$input-border: #DEDEDE;
$header-border-color: #EAE9E9;
$gray-color:#E5E5E5;
$border-right-color: #D8D8D8;
$strick-color: #B7B7B7;

$heading-color:#646464;
$hover-color: #d7df21;
$active-color: #0A72AB;
// $alert-color: #f04124;
// $success-color:#43AC6A;
// $warning-color: #f08a24;
// $info-color: #a0d3e8;
// $light-text:#858585;

$facebook-color: #3b5998;
$twitter-color: #00aced;
$linkedin-color: #007bb5;
$email-color: #dd4b39;

/*======Variable color end=======*/

$font-weight-thin: 100;
$font-weight-extralight: 200;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// responisve
// responisve
@mixin responsive($breakpoint) {
    @if $breakpoint==iphone6 {
        @media (min-width: 375px) {
            @content;
        }
    }
    @else if $breakpoint==phone6plus {
        @media (min-width: 414px) {
            @content;
        }
    }
    @else if $breakpoint==phone {
        @media (min-width: 480px) {
            @content;
        }
    }
    @else if $breakpoint==iphone {
        @media (max-width: 640px) {
            @content;
        }
    }
    @else if $breakpoint==tab {
        @media (max-width: 768px) {
            @content;
        }
    }
    @else if $breakpoint==desktop {
        @media (min-width: 992px) {
            @content;
        }
    }
    @else if $breakpoint==xbig {
        @media (max-width: 1200px) {
            @content;
        }
    }
}
