import axios from 'axios';

export function PostData(type, exactFilenameWithDir, userPostData) {
    let BaseURL = 'https://www.evisionstore.com/api/';

    return new Promise((resolve, reject) => {
        if(type==="fetch"){
            fetch(BaseURL+exactFilenameWithDir, {
                method: 'POST',
                body: JSON.stringify(userPostData)
            })
            .then((response) => 
                response.json()
            )
            .then((res) => {
                resolve(res);
            })
            .catch((error) => {
                reject(error);
            });
        }
        if(type==="axios"){
            var config = {
                headers: {'Content-Type': 'application/json'}
                //headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            };

            axios.post(BaseURL+exactFilenameWithDir,  userPostData, config)
            .then((response) => 
                response.json()
            )
            .then((res) => {
                resolve(res);
            })
            .catch((error) => {
                reject(error);
            });
        }
   });
}