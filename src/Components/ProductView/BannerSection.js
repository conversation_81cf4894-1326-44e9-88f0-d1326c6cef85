import React, {Component} from 'react';
import { with<PERSON>out<PERSON> } from "react-router";
import { Helmet } from 'react-helmet';
import { <PERSON> } from "react-router-dom";
import API_BASE_URL from '../../config/api';

class BannerSection extends Component {

    constructor(props) {
        super(props);
        this.state = {
            error: '',
            productName : '',
            productCategoryId: '',
            productCategory: '',
            productCategorySlug: '',
            isProductLoaded: false
        }
    }

    handleBackClick = () => {
        this.props.history.goBack(); // Goes back to previous page
      };

    async componentDidMount() {
        let product_id = this.props.match.params.productId;
        //const apiUrl = 'https://www.evisionstore.com/api/product/get_title_by_id.php';
        const apiUrl = `${API_BASE_URL}/title`;

        let requestOptions;
        if (isNaN(product_id)) {
            requestOptions = {
                method: 'POST',
                body: JSON.stringify({"model_number": product_id})
            };
        } else {
            requestOptions = {
                method: 'POST',
                body: JSON.stringify({"product_id": product_id})
            };
        }
        try {
            const response = await fetch(apiUrl, requestOptions);
            const result = await response.json();
            this.setState({
                productName: result.data.product_name,
                productCategoryId: result.data.category_id,
                productCategory: result.data.category_name,
                productCategorySlug: result.data.category_slug,
                isProductLoaded: true
            });
        } catch (error) {
            this.setState({ error: error });
        }
        const backButton = document.querySelector('.product-details-header__svg');
    if (backButton) {
      backButton.addEventListener('click', this.handleBackClick);
    }
    }

    componentWillUnmount() {
        const backButton = document.querySelector('.product-details-header__svg');
        if (backButton) {
          backButton.removeEventListener('click', this.handleBackClick);
        }
      }

    async componentDidUpdate(prevProps, prevState) {
        // only update chart if the data has changed
        if (prevProps.match.params.productId !== this.props.match.params.productId) {
            let product_id = this.props.match.params.productId;
            //const apiUrl = 'https://www.evisionstore.com/api/product/get_title_by_id.php';
            const apiUrl = `${API_BASE_URL}/title`;
            let requestOptions;
            if (isNaN(product_id)) {
                requestOptions = {
                    method: 'POST',
                    body: JSON.stringify({"model_number": product_id})
                };
            } else {
                requestOptions = {
                    method: 'POST',
                    body: JSON.stringify({"product_id": product_id})
                };
            }
            try {
                const response = await fetch(apiUrl, requestOptions);
                const result = await response.json();
                this.setState({
                    productName: result.data.product_name,
                    productCategoryId: result.data.category_id,
                    productCategory: result.data.category_name,
                    productCategorySlug: result.data.category_slug,
                    isProductLoaded: true
                });
            } catch (error) {
                this.setState({ error: error });
            }
        }
    }

    render(){
        const { productName, productCategoryId, productCategory, productCategorySlug} = this.state; 
        const hostname = window.location.host;
        let params = this.props.match.params;
        //var canonicalUrl = "https://" + hostname + "/producto/" + params.productId;
        return(
            <>

            <section className="product-details-header">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-xs-8 d-flex align-items-center">
                            <div className="product-details-header__svg">
                                <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7 13L1 7L7 1" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h4 className="product-details-header__title">Product Details</h4>
                        </div>
                        {/* <div className="col-xs-4 d-flex justify-content-end">
                            <a href="javascript:void(0)" className="product-details-header__svg">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.0758 15.8333C13.7577 15.8333 16.7425 12.8486 16.7425 9.16667C16.7425 5.48477 13.7577 2.5 10.0758 2.5C6.39395 2.5 3.40918 5.48477 3.40918 9.16667C3.40918 12.8486 6.39395 15.8333 10.0758 15.8333Z" stroke="#131921" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M18.4092 17.5L14.7842 13.875" stroke="#131921" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </a>
                        </div> */}
                    </div>
                </div>
            </section>
            {/* <Helmet>
                <link rel="canonical" href={canonicalUrl} />
            </Helmet> */}
            {/* <section className="banner-container product-banner-container">
                <div className="product-title" dangerouslySetInnerHTML={{__html: productName }} />
                <figure> 
                    <img src={`${process.env.PUBLIC_URL}/images/header-bg.jpg`} alt="" /> 
                </figure>		      
            </section> */}

            <section className="breadcamp product-breadcamp-container">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <Link to="">Home</Link><span className="separator">&#62;</span><Link to={`/categorias/${productCategorySlug}`}>{productCategory}</Link><span className="separator">&#62;</span><span className="bread-product-name" dangerouslySetInnerHTML={{__html: productName }} />
                        </div>
                    </div>
                </div>
            </section>
            </>
        )
    }
}

export default withRouter(BannerSection);