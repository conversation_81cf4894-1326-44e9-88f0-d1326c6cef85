* {
	margin: 0; padding: 0; border: 0;
}

body {
	font-family: arial, helvetica, serif;
	background: #1A1B1F url("../images/fondo1.jpg") top left no-repeat fixed;
	background-size: cover;
}

h1{
	position: absolute;
	top: 1.15em;
	right: .5em;
	width: 250px;
	height: 74px;
	background: url("../images/logo_evision-notrans.png") no-repeat;
	text-indent: -8888px;
}

/*  =================================Menu drop down=================================== */
#menu, #menu ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

#menu {
    width: 100%;
    margin: 0 auto;
    border: 1px solid #222;
    background-color: #111;
    background-image: linear-gradient(#444, #111);
}
#menu:before,
#menu:after {
    content: "";
    display: table;
}

#menu:after {
    clear: both;
}

#menu {
    zoom:1;
}

#menu li {
    float: left;
    border-right: 1px solid #222;
    position: relative;
}

#menu a {
    float: left;
    padding: 6px 30px;
    color: #999;
    text-transform: uppercase;
    font-weight: bold;
    text-decoration: none;
    text-transform: capitalize;
    font-size: 1.15em;
}

#menu li:hover > a {
    color: #fafafa;
}

*html #menu li a:hover { /* IE6 only */
    color: #fafafa;
}
#menu ul {
    margin: 20px 0 0 0;
    _margin: 0; /*IE6 only*/
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 30px;
    left: 0;
    z-index: 1;    
    background: #444;   
    background: linear-gradient(#444, #111);
    box-shadow: 0 -1px 0 rgba(255,255,255,.3);  
    border-radius: 3px;
    transition: all .2s ease-in-out;
}

#menu li:hover > ul {
    opacity: 1;
    visibility: visible;
    margin: 0;
}

#menu ul ul {
    top: 0;
    left: 150px;
    margin: 0 0 0 20px;
    _margin: 0; /*IE6 only*/
    box-shadow: -1px 0 0 rgba(255,255,255,.3);      
}

#menu ul li {
    float: none;
    display: block;
    border: 0;
    _line-height: 0; /*IE6 only*/
    box-shadow: 0 1px 0 #111, 0 2px 0 #666;
}

#menu ul li:last-child {   
    box-shadow: none;    
}

#menu ul a {    
    padding: 10px;
    /*width: 130px;*/
    _height: 10px; /*IE6 only*/
    display: block;
    white-space: nowrap;
    float: none;
    text-transform: none;
}

#menu ul a:hover {
    background-color: #0186ba;
    background-image: linear-gradient(#04acec, #0186ba);
}

#menu ul li:first-child > a {
    border-radius: 3px 3px 0 0;
}

#menu ul li:first-child > a:after {
    content: '';
    position: absolute;
    left: 40px;
    top: -6px;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #444;
}

#menu ul ul li:first-child a:after {
    left: -6px;
    top: 50%;
    margin-top: -6px;
    border-left: 0; 
    border-bottom: 6px solid transparent;
    border-top: 6px solid transparent;
    border-right: 6px solid #3b3b3b;
}

#menu ul li:first-child a:hover:after {
    border-bottom-color: #04acec; 
}

#menu ul ul li:first-child a:hover:after {
    border-right-color: #0299d3; 
    border-bottom-color: transparent;   
}

#menu ul li:last-child > a {
    border-radius: 0 0 3px 3px;
}
/*  =================================      Final    =================================== */		

#content {
	margin: 1em;
	color: #000;
    min-height: 600px;
}

form, .buscar {
	background-color: rgba(255,255,255,.5);
	padding: .5em;
	border-radius: .5em;
	margin-bottom: 1em;
}

form p{
	margin-bottom: .5em;
}

legend {
	font-size: 1.5em;
	font-weight: bold;
	color: #000;
	margin-bottom: .5em;
}

input {
	border: 1px solid #CCC;
	line-height: 1.5em;
}

input[type=submit]{
	color: #FFF;
	background-color: #0186ba;
    background-image: linear-gradient(#04acec, #0186ba);
    padding: 0 .5em;
}

input[type=text]{
width: 8em;
}

input[type=file]{
width: 50em;
}

textarea{
width: 30em;
height: 15em;
overflow: auto;}

select {
width: 15em;}

label {
width: 8em;
float: left;
margin-right: 1em;
text-align: right;
color: #333333;
font-weight: bold;
font-size: .85em!important;
}

table, td, tr, th {
border: 1px solid #000000;
border-collapse: collapse;
color: #000000;
}

td{
padding: .25em;
vertical-align: top;
}

thead{
background-color: #CCCCCC;}

caption {
font-weight: bold;
font-size: 1.5em;
padding-bottom: 1em;}

footer {
	color: #FFF;
	font-size: .75em;
	margin-top: 2em;
	height: 3em;
    text-align: center;
}

input:invalid {
 
border: 1px solid red;
 
}
 
/* Estilo por defecto */
 
input:valid {
 
border: 1px solid green;
 
}

button {
    padding: .5em 2em;
    color: #FFF;
    background-color: #0186ba;
    background-image: linear-gradient(#04acec, #0186ba);
}
.rslides figure{
    position: relative;
    margin:12px;
}



.rslides figcaption{
    position: absolute;
    top:0;
    left: 0;
    background-color: rgba(45, 50, 54, 0.75);
    font-size: .95em;
    padding: 1px;
    width: 180px;
    color: #FFF!important;
    text-decoration: none;
    display: block
}

.rslides figcaption:first-line {
    font-weight: bold!important;
    font-size: 1.25em;
}

.rslides strong {
    display: block;
    margin-top: .5em;
    font-size: 2em;
    font-weight: bold;
}

.rslides button {
    position: absolute;
    bottom: 5em;
    left: 25em;
}

/*-------------------------24-08-17---------------------*/

.vieworder-grid {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 0.5em;
    margin-bottom: 1em;
    padding: 0.5em;
}

.vieworder-grid ul.form-list li {
    display: inline-block;
    list-style: outside none none;
    margin-bottom: 20px;
    position: relative;
    width: 100%;
}
.order.name-first {
    float: left;
    width: 50%;
}
.order.name-last {
    float: left;
    width: 50%;
}

h3 { padding: 8px 0; } 
#order-det {
	border:none;
}
label { float:none; }

.form-list2, .form-list2 td, .form-list2 tr, .form-list2 th { border: 0 ; }

.items_grid{ width : 100% ; }
.items_grid th, .items_grid td{ padding:5px; text-align:left; }
.customer-grid th, .customer-grid td{ padding:5px; }


.order-grid {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 0.5em;
    margin-bottom: 1em;
    padding: 0.5em;
}

.customer-grid {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 0.5em;
    margin-bottom: 1em;
}

.accounts-form {
    margin-bottom:10px;
}
.h4 { 
    margin-bottom:10px;
}

.customer-account-edit .accounts-form label, #catalogo_insert label, #tax_insert label, .insert_cats label, .edit_cats label, #catalogo_edit label,.offertas_insert label, .offertas_edit label, .slides_insert label,
#catalogo_edit label{ float: left; width: 10%; display: block; text-align: left; }
.customer-account-edit .accounts-form .input_form, #catalogo_insert .input_type, #tax_insert .input_type, .insert_cats .input_type, .edit_cats .input_type, #catalogo_edit .input_type, .offertas_insert .input_type, .offertas_edit .input_type, .slides_insert .input_type,  
#catalogo_edit .input_type{ width: 30%; padding: 6px; box-sizing: border-box; }


ul.pagination {
    text-align:center;
    color:#0186ba;
    margin-top: 10px;
}
ul.pagination li {
    display:inline;
    padding:0 3px;
}
ul.pagination a {
    color:#0186ba;
    display:inline-block;
    padding:5px 10px;
    border:1px solid #cde0dc;
    text-decoration:none;
}
ul.pagination a:hover, 
ul.pagination a.current {
    background:#0186ba;
    color:#fff; 
}

/*-----------------------30-08-17------------------------*/

.customer-new{ text-align: right; padding: 10px 10px 0 0; }
.customer-new button{ padding: 0 20px; height: 40px; line-height: 40px; cursor: pointer; outline: 0; }
.customer-new button img{ float: left; margin:9px 10px 0 0; } 






