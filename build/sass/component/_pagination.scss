
  @keyframes cdp-in {
    from {
      transform: scale(1.5);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .cdp {
    position: relative;
    display: inline-block;
    text-align: center;
    padding: 20px 0;
    font-size: 0;
    z-index: 6;
    // margin: 50px 0;
    
    animation: cdp-in 500ms ease both;
    animation-timeout: 200ms;
  
    &_i {
      font-size: 14px;
      text-decoration: none;
      
      transition: background 250ms;
      
      display: inline-block;
      text-transform: uppercase;
      margin: 0 3px 6px;
      height: 36px;
      min-width: 36px;
      border-radius: 38px;
      background-color: #F1EFEF;
      line-height: 38px;
      padding: 0;
      color: #fff;
      font-weight: 400;
      font-family: $primary-font;
      font-size: 15px;
      color: #454545;
      display: none;
  
      &:first-child,
      &:last-child {
        // padding: 0 16px;
        // margin: 0 12px 6px;
      }
  
      &:last-child,
      &:nth-child(2),
      &:nth-last-child(2) {
        display: inline-block;
      }
    }
  
    &_i:hover {
      background-color: #000;
      color: #fff;
    }
  
    &:not([actpage="1"]) &_i:nth-child(1) {
      display: inline-block;
    }
  }
  @for $i from 1 through 80 {
    .cdp[actpage="#{$i}"] {
      // 3 before
      .cdp_i:nth-child(#{$i - 2}):not(:first-child):not(:nth-child(2)) {
        display: inline-block;
        pointer-events: none;
        color: transparent;
        border-color: transparent;
        width: 50px;
        &:after {
          content: '...';
          color: #fff;
          font-size: 32px;
          margin-left: -6px;
        }
      }
      // 2 before
      .cdp_i:nth-child(#{$i - 1}):not(:first-child) {
        display: inline-block;
      }
      // before
      .cdp_i:nth-child(#{$i}):not(:first-child) {
        display: inline-block;
      }
      // active
      .cdp_i:nth-child(#{$i + 1}) {
        background-color: #000;
        color: #fff;
        display: inline-block;
  
        +.cdp_i:last-child {
          display: none !important;
        }
      }
      // next
      .cdp_i:nth-child(#{$i + 2}):not(:last-child) {
        display: inline-block;
      }
      // 2 next
      .cdp_i:nth-child(#{$i + 3}):not(:last-child) {
        display: inline-block;
      }
      // 3 next
      .cdp_i:nth-child(#{$i + 4}):not(:last-child):not(:nth-last-child(2)) {
        display: inline-block;
        pointer-events: none;
        color: transparent;
        border-color: transparent;
        width: 50px;
        &:after {
          content: '...';
          color: #fff;
          font-size: 32px;
          margin-left: -6px;
        }
      }
    }
  }
  .pagination-break
  {
      background-color: transparent;
      pointer-events: none;
  }
  .o-pagination-active
  {
      background-color: $secondary-color;
      color: $white;
  }