// @include responsive(tab) {

// }

// header{
//   &.mobile{
//     display: none;
//     @include responsive(tab) {
//       display: block;
//     }
//   }
// }

.logo-area {
  max-width: 129px;
}
.header-top-area {
  .main-menu-container {
    background: transparent;
    .container {
      padding: 0;
      display: block;
      @media only screen and (min-width: 1024px) {
        display: flex;
      }
    }
  }
  .navbar-nav {
    display: flex;
    li {
      margin-right: 15px;
      &:last-child {
        margin-right: 0;
      }
      a {
        font-size: 16px;
        line-height: 24px;
        color: #47545b !important;
        font-weight: 400;
        padding: 10px 0;
        @media (max-width: 1140px) {
          font-size: 14px;
        }

        &:hover {
          color: #ff002a !important;
          @media (max-width: 767px) {
            color: #000 !important;
          }
        }
      }
    }
  }
}

/*=========================================
2. <PERSON><PERSON> Common Styles                                
==========================================*/
.banner-slider-container {
  .slick-slide {
    img {
      max-height: 450px;
    }
  }
}

.modal-header .close {
  padding: 1rem;
  margin: -25px 15px 0 0 !important;
}

.intl-tel-input.allow-dropdown.separate-dial-code .selected-dial-code {
  font-size: 12px;
  padding-left: 2px !important;
}
.iti-flag {
  margin: 11px 5px 0 5px !important;
}

.intl-tel-input.allow-dropdown.separate-dial-code.iti-sdc-2 input,
.intl-tel-input.allow-dropdown.separate-dial-code.iti-sdc-2 input[type="tel"],
.intl-tel-input.allow-dropdown.separate-dial-code.iti-sdc-2 input[type="text"] {
  padding-left: 96px !important;
}

.slick-next,
.slick-prev {
  display: none !important;
}

.navbar {
  position: relative;
  padding: 0.5rem 1rem;
  width: 100% !important;
}
.navbar-nav {
  display: block !important;
  flex-direction: none !important;
  /* padding-left: 0; */
  margin-bottom: 0;
  list-style: none;
}

.slick-initialized .slick-slide {
  margin: 0px !important;
  @media (max-width: 1024px) {
    text-align: center;
  }
}
.slick-slider .item {
  display: block !important;
}

.slick-slide img {
  display: inline-block !important;
  /* max-height: 214px; */
}

.cart-counter {
  width: 20px;
  height: 20px;
  position: absolute;
  border-radius: 50%;
  overflow: hidden;
  background: #ff002a;
  color: #fff;
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  margin: -5px 0 0 -10px;
  left: 19px;
  @media (max-width: 767px) {
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin: -18px 0 0 5px;
  }
}

.fluid__image-container {
  flex: 0 0 30%;
}

.fluid__instructions {
  flex: 0 0 50%;
}

.fixed__instructions {
  flex: 1;
  margin: 0 20px;
}

.portal {
  position: absolute;
  top: 0px;
  left: -21px;
  z-index: 999999;
  overflow: hidden;
}

.header-top-section .header-phone ul li a {
  color: #333;
  text-decoration: none;
}
.header-top-section .header-phone ul li a:hover {
  color: #000;
  text-decoration: none;
}

.header-top-section {
  background: #fff;
  .container {
    padding: 14px 0 10px;
    border-bottom: 1px solid #daddde;
    @media only screen and (max-width: 767px) {
      padding: 6px 0 6px;
    }
  }
}
.header-top-section ul {
  text-align: left;
  margin: 0;
  @media only screen and (max-width: 767px) {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
  }
}
.header-top-section ul li {
  display: inline-block;
  color: #333;
  font-size: 14px;
  margin-right: 10px;
  @media only screen and (min-width: 1024px) {
    margin-right: 20px;
  }
}
.header-top-section ul li i {
  color: #333;
  padding-right: 10px;
}
header-top-section .header-address {
  padding-top: 15px;
}

.header-top-section .header-address ul {
  text-align: right;
}

.header-top-section .header-address ul li {
  display: inline-block;
  color: #333;
  font-size: 14px;
  padding-right: 10px;
  margin-right: 10px;
  border-right: 1px solid #ccc;
  line-height: 14px;
}
.header-top-section .header-address ul li a {
  color: #333;
  text-decoration: none;
}
.header-top-section .header-address ul li a:hover {
  color: #000;
  text-decoration: none;
}

.header-top-section .header-address ul li:last-child {
  padding-right: 0px;
  margin-right: 0px;
  border-right: 0px solid #fff;
}

.header-top-section .header-address ul li {
  display: inline-block;
  color: #333;
  font-size: 14px;
  padding-right: 10px;
  margin-right: 10px;
  border-right: 0px solid #ccc;
  line-height: 14px;
}
.header-top-section .header-address ul li a {
  color: #47545b;
  font-size: 12px;
  text-decoration: none;
  display: flex;
  align-items: center;
  .icon-float {
    margin-right: 5px;
  }
}
.header-top-section .header-address ul.dropdown-menu li a {
  color: #000;
  text-decoration: none;
}
.cart-wrap-header {
  position: relative;
}
.header-address .dropdown-menu {
  box-shadow: none;
  border-radius: 0;
  border: none;
  z-index: 99999999;
  margin-top: 0;
  text-align: left;
  border: 1px solid #ccc;
}

.caret-up {
  width: 0;
  height: 0;
  border-left: 4px solid rgba(0, 0, 0, 0);
  border-right: 4px solid rgba(0, 0, 0, 0);
  border-bottom: 4px solid;

  display: inline-block;
  margin-left: 2px;
  vertical-align: middle;
}
.after-login-menu .nav > li {
  padding: 0px 11px 0px 0px !important;
  color: #fff;
  background: none !important;
  border-right: 1px solid #fff;
  line-height: 14px;
  margin-top: 5px;
}

.after-login-menu .nav > li:last-child {
  padding: 0px 11px 0px 0px !important;
  color: #fff;
  background: none !important;
  border-right: none;
  line-height: 14px;
}
.after-login-menu .nav > li > a {
  padding: 0px 11px 0px 0px !important;
  color: #fff;
  background: none !important;
  line-height: 14px;
}
.after-login-menu .nav > li > a:hover {
  padding: 0px 11px 0px 0px !important;
  color: #fff;
  background: none !important;
  line-height: 14px;
}

.after-login-menu .navbar-nav > li > .dropdown-menu {
  margin-top: 10px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.after-login-menu .dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
  float: left;
  width: 100%;
}
.after-login-menu .dropdown-menu > li > a:hover {
  color: #ff002a;
  text-decoration: none;
}
.after-login-menu .dropdown-menu {
  background: #ffffff;
  border: 1px solid #eceeef;
}
.after-login-menu .dropdown-menu:after,
.after-login-menu .dropdown-menu:before {
  bottom: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.after-login-menu .dropdown-menu:after {
  border-color: rgba(255, 255, 255, 0);
  border-bottom-color: #fff;
  border-width: 10px;
  margin-left: -10px;
}
.after-login-menu .dropdown-menu:before {
  border-color: rgba(255, 255, 255, 0);
  border-bottom-color: #fff;
  border-width: 10px;
  margin-left: -10px;
}

.logo-area {
  display: table;
  height: 78px;
  position: relative;
  @media (max-width: 767px) {
    margin-left: auto;
    margin-right: auto;
    height: 24px !important;
    width: 88px;
  }
}
.logo-area a {
  display: table-cell;
  vertical-align: middle;
}
.header-shadow {
  -webkit-box-shadow: 0 1px 4px 0px rgba(46, 50, 50, 0.5);
  -moz-box-shadow: 0 1px 4px 0px rgba(46, 50, 50, 0.5);
  box-shadow: 0 1px 4px 0px rgba(46, 50, 50, 0.5);
  margin-bottom: 5px;
}

/*------------------------------------CAT TAB CSS------------------------------*/
.recharge-field-container {
  float: left;
  width: 100%;
  background: #fff;
  margin-top: -75px;
  min-height: 200px;
  border-radius: 3px;
  padding: 15px;
  -webkit-box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

/*.recharge-field-container td {padding:0px !important; margin:0px !important; border:none !important;}
  .recharge-field-container tr {padding:0px !important; margin:0px !important; border:none !important;}
  .recharge-field-container th {padding:0px !important; margin:0px !important; border:none !important;}
  .recharge-field-container table {padding:0px !important; margin:0px !important; border:none !important;}*/
/*-------------------------------------------
  2.1 Headr Two Styles
  -------------------------------------------*/
.header-two {
  background: #fff;
  padding: 0;
  @media (max-width: 767px) {
    background: rgb(242, 250, 255);
    background: linear-gradient(
      180deg,
      rgba(242, 250, 255, 1) 0%,
      rgba(174, 225, 255, 1) 100%
    );
    padding: 10px 0;
  }
}
.header-two .stick {
  background: #ffffff;
}
.header-two .main-menu nav ul > li a {
  color: #333;
}
.header-two .main-menu nav ul > li a:hover {
  color: #333;
}
.header-two .main-menu nav ul > li.active > a {
  color: #333;
}
.header-two .main-menu nav ul ul li a {
  color: #333;
}
.header-two .main-menu nav ul ul li a:hover {
  color: #000;
}
.header-two .header-top-right .search-button {
  color: #333;
  border-right: 1px solid #333;
}
.header-two .header-top-right .cart-area .flaticon-commerce::before {
  color: #333;
}

.header-right-section {
  float: right;
}

/*-------------------------------------------
  2.3 Main Menu Styles
  -------------------------------------------*/

.navbar-nav .dropdown-menu {
  position: absolute !important;
  float: none;
  width: 300px;
  top: 57px;
  @media (max-width: 767px) {
    position: relative !important;
    float: left !important;
    width: 280px;
    top: 0 !important;
    height: 100%;
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #2e6c91;
    box-shadow: none;
    border: none;
    margin-bottom: 15px;
    &::-webkit-scrollbar {
      width: 2px;
    }
    &::-webkit-scrollbar-track {
      background: #0075b9;
    }
    &::-webkit-scrollbar-thumb {
      background: #2e6c91;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #1a1a1a;
    }
  }
}

.navbar-nav .dropdown-menu li {
  float: left;
  width: 100%;
  border-bottom: 1px solid #e6e6e6;
  @media (max-width: 767px) {
    border-bottom: 1px solid #3c82ab;
  }
}

/* .navbar-nav .submenu-arrow:before, .navbar-nav .submenu-arrow:after {
      border-right: 2px solid;
      content: '';
      display: block;
      height: 8px;
      margin-top: 15px;
      position: absolute;
      -moz-transform: rotate(135deg);
      -o-transform: rotate(135deg);
      -webkit-transform: rotate(135deg);
      transform: rotate(135deg);
      right: 10px;
      width: 0;
  }
   */

.dropdown-menu > li > a {
  display: block;
  padding: 9px 20px !important;
  clear: both;
  color: #47545b;
  white-space: nowrap;
}

.navbar-nav:not(.mobile-navbar-nav) .dropdown-menu li ul {
  position: absolute;
  display: none;
  background: #fff !important;
  border: 0px solid #ccc;
  padding: 0px;
  margin: -30px 0 0 20px;
  left: 250px;
  width: 300px;
  border-radius: 0 3px 3px 0px;
  -webkit-box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
}

.navbar-nav:not(.mobile-navbar-nav) .dropdown-menu li:hover ul {
  position: absolute;
  display: block;
  background: #fff !important;
  border: 0px solid #ccc;
  padding: 0px;
  margin: -39px 0 0px 26px;
  left: 272px;
  width: 271px;
  border-radius: 0 3px 3px 0px;
  -webkit-box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
  box-shadow: -1px 1px 5px 0px rgba(0, 0, 0, 0.1);
}

.navbar-nav:not(.mobile-navbar-nav) .dropdown-menu li ul li {
  float: left;
  width: 100% !important;
  border-bottom: 1px solid #e6e6e6;
}

.submenu-arrow:after {
  margin-top: -19px;
  -moz-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.submenu-arrow:after {
  border-right: 2px solid;
  content: "";
  display: block;
  height: 8px;
  margin-top: -25px;
  position: absolute;
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
  transform: rotate(135deg);
  right: 18px;
  width: 0;
}

.submenu-arrow:before {
  border-right: 2px solid;
  content: "";
  display: block;
  height: 8px;
  position: absolute;
  margin-top: 22px;
  -moz-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  right: 18px;
  width: 0;
}

.navbar-nav .dropdown-menu li ul li a {
  display: block;
  padding: 10px 15px;
  clear: both;
  line-height: 1.42857143;
  color: #47545b;
  white-space: nowrap;
  float: left;
  width: 100% !important;
  font-size: 16px;
}

.navbar-nav .dropdown-menu li ul li a:focus,
.navbar-nav .dropdown-menu li ul li a:hover {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5;
  float: left;
  width: 100% !important;
}

.main-menu ul {
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}

.main-menu ul li span {
  font-size: 10px;
  line-height: 26px;
  width: 100%;
  float: left;
  margin-top: -12px;
}
.offer {
  width: 97px;
  margin-top: -20px;
  float: left;
}

.offer a {
  line-height: 30px !important;
}

.main-menu ul li {
  display: inline-block;
  position: relative;
}
.main-menu > ul > li > a {
  line-height: 40px;
}
.main-menu > ul > li > a img {
  @media (max-width: 767px) {
    width: 24px !important;
  }
  &.cart {
    @media (max-width: 767px) {
      width: 18px !important;
      margin: 0 !important;
    }
  }
}
.main-menu ul li a {
  display: block;
  color: #47545b;
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  line-height: 24px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
  @media (max-width: 1180px) {
    line-height: 24px;
  }
  @media (max-width: 767px) {
    line-height: 43px;
  }
  img {
    margin: 0 0 0 0;
    @media (max-width: 1180px) {
      display: block;
      margin: auto;
    }
    @media (max-width: 767px) {
      display: inline-block;
      margin-bottom: 0 3px 0 0;
    }
  }
}
.main-menu ul li ul li a {
  line-height: 20px;
}

.main-menu ul > li.active > a,
.main-menu ul > li > a:hover {
  color: #1675b9;
  text-decoration: none;
}
.main-menu ul ul {
  background: #fff;
  right: 0;
  opacity: 0;
  position: absolute;
  transform: scaleY(0);
  transform-origin: 0 0 0;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
  width: 200px;
  z-index: 99999;
  visibility: hidden;
}
.main-menu ul li:hover ul {
  opacity: 1;
  transform: scaleY(1);
  visibility: visible;
  margin: 0px;
  border: 1px solid #ddd;
}
.main-menu ul li ul li {
  display: block;
  border-bottom: 1px solid #dddddd;
}
.main-menu ul li ul li:last-child {
  border-bottom: 0px solid #dddddd;
}
.main-menu ul li ul li a {
  color: #333;
  display: block;
  font-size: inherit;
  font-size: 14px;
  font-weight: normal;
  padding: 8px 15px 10px 15px;
  text-align: left;
  text-decoration: none;
  text-transform: none;
}
.main-menu ul li ul li a:hover {
  color: #fff;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  background: #ff002a;
}

/* MAIN NAVIGATION */
.main-menu-container {
}

.navbar-collapse {
  padding-right: 0px !important;
  padding-left: 0px !important;
}

.navbar-default .navbar-brand {
  color: #fff !important;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #fff !important;
}
.navbar-brand {
  display: none;
}

.navbar-default {
  background: none !important;
  border: none !important;
}
.navbar {
  position: relative;
  min-height: auto !important;
  margin-bottom: 0px !important;
  border: 0px solid transparent !important;
}
.navbar-default .navbar-nav > li > a:focus,
.navbar-default .navbar-nav > li > a:hover {
  // color: #fff !important;
  // opacity: 0.5;
  // background-color: transparent;
}

.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -moz-border-radius: 0 6px 6px;
  border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

.dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}

.dropdown-submenu:hover > a:after {
  border-left-color: #fff;
}

.dropdown-submenu.pull-left {
  float: none;
}

.dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}

.CartDescription a {
  color: #333333;
}
.btn-group-lg > .btn,
.btn-lg {
  padding: 5px !important;
  font-size: 16px !important;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.caps {
  text-align: right;
}
@media (max-width: 640px) {
  .table-block {
    float: left !important;
    width: 100% !important;
  }
  .btn-block {
    display: block;
    width: 100% !important;
    float: left !important;
  }
  .btn-group-lg > .btn,
  .btn-lg {
    font-size: 14px !important;
  }
  pre {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
}
@media (max-width: 767px) {
  pre {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .btn-group-lg > .btn,
  .btn-lg {
    font-size: 14px !important;
  }
  .table-block {
    float: left !important;
    width: 100% !important;
  }
  .btn-block {
    display: block;
    width: 100% !important;
    float: left !important;
  }

  .main-menu ul ul {
    right: 0 !important;
  }

  .navbar-brand {
    display: block;
  }

  .navbar-nav {
    margin: 7.5px 0px !important;
  }
}

/*search box*/

.autocomplete {
  border: 1px solid #c9e2f0;
  position: absolute;
  background: #fff;
  z-index: 999999999;
  padding: 15px 0;
  @media (max-width: 767px) {
    margin-top: 50px;
    width: 93%;
    box-sizing: border-box;
  }
  @media (min-width: 768px) {
    margin-top: 15px;
  }
  max-width: 540px;
  max-height: 425px;
  overflow-y: auto;
  width: 100%;
  border-radius: 5px;
}

.autocomplete li {
  border-bottom: 1px solid #c9e2f0;
  padding: 10px 15px;
}
.autocomplete li:last-child {
  border-bottom: none;
}
.autocomplete a {
  color: #333;
  text-decoration: none;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.autocomplete a:hover {
  color: #000;
  text-decoration: none;
}

ul.autocomplete .img-sec {
  float: left;
  width: 20%;
  min-width: 70px;
}
ul.autocomplete .img-sec .prod-img {
  max-width: 70px;
}

ul.autocomplete .desc-sec {
  float: left;
  width: 80%;
  position: relative;
  padding-left: 10px;
}
ul.autocomplete .desc-sec p {
  margin-bottom: 5px !important;
  color: #3c525d;
  font-size: 14px;
}
ul.autocomplete .desc-sec p mark {
  background: 0 0;
  font-weight: 600;
  padding: 0;
}
ul.autocomplete .sep-sec {
  float: none;
  clear: both;
}

ul.autocomplete .instock-sec {
  font-weight: 500;
  color: #088918 !important;
}
ul.autocomplete .outstock-sec {
  font-weight: 500;
  color: #ff002a !important;
}
ul.autocomplete .price-span {
  font-weight: 500;
  color: #47545b;
}
ul.autocomplete .sell-price-span {
  color: #ff002a;
}
ul.autocomplete .reg-price-span {
  text-decoration: line-through;
  font-weight: 400;
  margin-left: 6px;
  color: rgb(248, 122, 122);
}

.input-group-btn {
  float: right !important;
  width: 16px !important;
  padding: 0px !important;
  text-align: center;
  font-size: 14px !important;
  line-height: 12px !important;
  line-height: 32px !important;
  height: 35px !important;
}

.bootstrap-touchspin .btn {
  padding: 0px !important;
}

.required-field {
  color: red;
  font-weight: 700;
  font-size: 14px;
  top: -0.3em;
}
.search_box_area {
  position: relative;
  width: 100%;
  form {
    position: relative;
  }
}
.search_box_area input[type="text"] {
  width: 100%;
  background-color: #ffffff;
  font-style: normal;
  color: #47545b;
  font-size: 14px;
  padding-left: 15px;
  padding-right: 40px;
  padding-top: 10px;
  padding-bottom: 8px;
  border: 1px solid #c9e2f0;
  border-radius: 35px;
  text-transform: capitalize;
  @media (max-width: 767px) {
    padding-left: 40px;
    padding-right: 15px;
    padding-top: 12px;
    padding-bottom: 11.5px;
    border: 1px solid #c1cdd9;
    border-radius: 8px;
    font-size: 15px;
  }
  &::placeholder {
    color: #47545b;
    opacity: 1;
  }
  &::-ms-input-placeholder {
    color: #47545b;
  }
}

.search_box_area button {
  position: absolute;
  color: #0075b9;
  font-size: 0px;
  border-left: none;
  cursor: pointer;
  background: transparent;
  padding: 0;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  @media (max-width: 767px) {
    color: #000;
    right: inherit;
    right: 15px;
  }
}

.search_box_area button:focus {
  // border: none !important;
  // outline: none !important;
  // outline: 0px auto -webkit-focus-ring-color !important;
}
header {
  @media (max-width: 767px) {
    // width: 100%;
    // overflow-x:hidden;
  }
}
.navbar-toggle {
  // margin-top: -51px;
  // margin-right: 40px;
  // background-color: #005D93 !important;
}
.navbar-collapse {
  padding: 0 40px !important;
  @media only screen and (min-width: 1024px) {
    padding: 0 !important;
  }
}
.main-menu {
  ul {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    @media (max-width: 767px) {
      justify-content: center;
      margin-top: 15px;
    }
  }
}

// MOBILE

.header-top-area {
  // @media (max-width: 767px) {
  //   background: rgb(174,225,255);
  //   background: linear-gradient(0deg, rgba(174,225,255,1) 35%, rgba(242,250,255,1) 100%);
  // }
}

.mobile img {
  // margin-top: 10px;
}

.mobile .main-menu ul {
  text-align: left;
  margin-top: 0;
  float: left !important;
}

.mobile .main-menu ul ul {
  text-align: left;
  margin-top: 50px !important;
  float: none !important;
  position: absolute;
}

/* .mobile .navbar {
    position: relative;
    min-height: auto !important;
    margin-bottom: 0px !important;
    border: 0px solid transparent !important;
} */

.mobile .navbar {
  position: relative;
  min-height: auto !important;
  margin-bottom: 0px !important;
  padding: 0 !important;
  margin: 0px 0px 0 0 !important;
  float: left;
  width: 41px !important;
}

.mobile .navbar-toggle {
  // position: relative !important;
  // float: right !important;
  // padding: 8px 10px !important;
  // background: #fff !important;
  // border-radius: 8px;
  // margin: 0;
  position: relative !important;
  float: right !important;
  padding: 0px 10px !important;
  background-image: none;
  border-radius: 8px;
  background: #fff;
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 40px;
  height: 40px;
  margin: 0;
}
.mobile .navbar-default .navbar-toggle .icon-bar {
  background-color: #000 !important;
}
.mobile .navbar-toggle .icon-bar {
  display: block;
  width: 20px !important;
  height: 2px !important;
  margin: 3px 0;
  &:last-child {
    width: 15px !important;
  }
}
.mobile .navbar-default .navbar-collapse,
.mobile .navbar-default .navbar-form {
  position: absolute;
  background: #0075b9;
  z-index: 99999999999 !important;
  left: 0px;
  width: 340px;
  top: 58px;
}
.mobile .navbar-nav li a {
  color: #fff !important;
  font-size: 16px !important;
  font-weight: 500;
}
.mobile-header-cart {
  background-color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  .cart-counter {
    margin: -14px 0 0;
  }
}

.searcharea {
  margin-top: 14px;
  .search_box_area {
    input[type="text"] {
      border-radius: 8px;
      padding: 10px 10px 10px 40px;
    }
    button {
      right: inherit;
      left: 5px;
      img {
        margin: 0;
      }
    }
  }
}

#root {
  @media (max-width: 767px) {
    overflow-x: hidden;
  }
}
.notify_prod_form {
  display: inline-block;
  padding: 30px 0 20px;
  h4 {
    font-family: "Poppins", sans-serif;
    font-size: 25px;
    line-height: 27px;
    font-weight: 600;
    color: #3c525d;
    text-align: center;
    margin: 0 0 10px !important;
  }
  p {
    font-family: "Poppins", sans-serif;
    color: #3c525d;
    font-size: 16px;
    text-align: center;
    margin-bottom: 30px;
  }
  .form-control {
    border: none;
    border-bottom: 1px solid #cfd7e5;
    color: #3c525d;
    font-size: 13px;
    line-height: 27px;
    margin-bottom: 0;
    padding: 10px;
    border-radius: 0px;
    background-color: transparent;
  }
}
.modal-body {
  .btn {
    border-radius: 30px;
    padding: 10px 15px !important;
    font-size: 14px !important;
  }
}
.modal-title.h4 {
  font-family: "Poppins", sans-serif;
  color: #3c525d;
  font-size: 17px;
  i {
    margin-right: 3px;
  }
}
.cartarea {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-search {
  margin: 0;
  @media (max-width: 767px) {
    margin: 70px 0 0;
  }
}
