import React, {Component} from 'react';
import { with<PERSON>outer } from "react-router";
import { <PERSON> } from "react-router-dom";

class BannerSection extends Component {
    constructor(props) {
        super(props);
        this.state = {
            error: '',
            promoName : '',
            isCategoryLoaded: false
        }
    }

    componentDidMount() {
        let params = this.props.match.params;
        let promoSlug = params.promoId;

        const apiUrl = 'https://www.evisionstore.com/api/product/get_title_and_image.php';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({"promo_slug": promoSlug})
        };
        fetch(apiUrl, requestOptions)
        .then(res => res.json())
        .then(
            (result) => {
                this.setState({
                    promoName: result.promo_name,
                    promoImage: result.promo_image_url,
                    isCategoryLoaded: true
                });
            },
            (error) => {
                this.setState({ error: error });
            }
        )
    }

    componentDidUpdate(prevProps, prevState) {
        // only update chart if the data has changed
        if (prevProps.match.params !== this.props.match.params) {
            
            this.setState({
                isCategoryLoaded: false
            });

            let params = this.props.match.params;
            let promoSlug = params.promoId;

            const apiUrl = 'https://www.evisionstore.com/api/product/get_title_and_image.php';
            const requestOptions = {
                method: 'POST',
                body: JSON.stringify({"promo_slug": promoSlug})
            };
            fetch(apiUrl, requestOptions)
            .then(res => res.json())
            .then(
                (result) => {
                    this.setState({
                        promoName: result.promo_name,
                        promoImage: result.promo_image_url,
                        isCategoryLoaded: true
                    });
                },
                (error) => {
                    this.setState({ error: error });
                }
            )
        }
    }

    render(){
        const {promoName, promoImage} = this.state;  

        return(
            <>
            <section className="banner-container">
                <div className="product-title">{promoName}</div>
                <figure> 
                <img src={`${process.env.PUBLIC_URL}/images/header-bg.jpg`} alt={promoName} />
                {/* {(() => {
                    if (promoImage !== ''){
                        return ( <img src={promoImage} alt={promoName} /> );
                    } else {
                        return ( <img src={`${process.env.PUBLIC_URL}/images/header-bg.jpg`} alt={promoName} /> );
                    }
                })()} */}
                </figure>		      
            </section>

            <section className="breadcamp">
            <div className="container">
                <div className="row">
                    <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <Link to="/">Home &raquo;</Link> {promoName}
                    </div>
                </div>
            </div>
            </section>
            </>
        )
        
    }
}

export default withRouter(BannerSection);