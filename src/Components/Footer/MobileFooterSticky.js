import React, { Component } from 'react';
import { <PERSON>, with<PERSON><PERSON><PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { Modal } from "react-bootstrap";
import { login2 } from "../../Services/Actions/user.actions";

class MobileFooterSticky extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activePath: props.location && props.location.pathname ? props.location.pathname : '',
      showModal: false,
      username: "",
      password: "",
      errors: {},
      loginLoader: "hidden",
      loginFormButtonDisabled: false,
      loginSubmitted: false,
      apiErrorMessage: "",
      apiSuccessMessage: "",
    };

    this.open = this.open.bind(this);
    this.close = this.close.bind(this);
    this.handleOnLoginInputChange = this.handleOnLoginInputChange.bind(this);
    this.handleOnLoginSubmit = this.handleOnLoginSubmit.bind(this);
    this.handleLoginFormValidation = this.handleLoginFormValidation.bind(this);
  }

  open() {
    this.setState({ showModal: true });
  }

  close() {
    this.setState({
      showModal: false,
      apiErrorMessage: "",
      apiSuccessMessage: "",
    });
  }

  handleOnLoginInputChange(e) {
    const { name, value } = e.target;
    this.setState({ [name]: value });

    let errors = this.state.errors;
    errors[name] = "";
    this.setState({ errors: errors });
  }

  handleLoginFormValidation() {
    let username = this.state.username;
    let password = this.state.password;
    let errors = {};
    let loginFormIsValid = true;

    if (!username) {
      loginFormIsValid = false;
      errors["username"] = "Email can not be empty";
    }

    if (typeof username !== "undefined") {
      let lastAtPos = username.lastIndexOf("@");
      let lastDotPos = username.lastIndexOf(".");

      if (
        !(
          lastAtPos < lastDotPos &&
          lastAtPos > 0 &&
          username.indexOf("@@") === -1 &&
          lastDotPos > 2 &&
          username.length - lastDotPos > 2
        )
      ) {
        loginFormIsValid = false;
        errors["username"] = "Email is not valid";
      }
    }

    if (!password) {
      loginFormIsValid = false;
      errors["password"] = "Password can not be empty";
    }

    this.setState({ errors: errors });
    return loginFormIsValid;
  }

  handleOnLoginSubmit(e) {
    e.preventDefault();
    this.setState({ loginSubmitted: true });

    if (this.handleLoginFormValidation()) {
      this.setState({ loginLoader: "", loginFormButtonDisabled: true });
      const { username, password } = this.state;
      const { cartItems } = this.props;

      this.props.logIn(username, password, cartItems);
      setTimeout(() => {
        if (this.props.isLoggedIn) {
          this.setState({
            apiSuccessMessage:
              "You have successfully logged in to your account.",
            loginFormButtonDisabled: false,
            loginLoader: "hidden",
          });
          setTimeout(() => this.close(), 2000);
        }
        if (this.props.isError) {
          this.setState({
            apiErrorMessage: this.props.errorMessage,
            loginFormButtonDisabled: false,
            loginLoader: "hidden",
          });
        }
      }, 2000);
    }
  }

  componentDidUpdate(prevProps) {
    // Detect route change
    if (this.props.location.pathname !== prevProps.location.pathname) {
      this.setState({ activePath: this.props.location.pathname });
    }
  }

  setActive = (path) => {
    this.setState({ activePath: path });
  };

  render() {
    const { cartCount, userAvatar, isLoggedIn } = this.props;
    const { activePath } = this.state;
    const {
      username,
      password,
      loginSubmitted,
      showModal,
      apiErrorMessage,
      apiSuccessMessage,
      loginLoader,
      loginFormButtonDisabled,
      errors,
    } = this.state;

    const apiError = {
      color: "red",
      fontSize: "16px",
      background: "#fff9b0",
      padding: "0px 5px 2px 5px",
      fontWeight: "normal",
      textAlign: "center",
      border: "1px solid red",
      borderRadius: "3px",
    };

    const apiSuccess = {
      color: "#316403",
      fontSize: "16px",
      background: "#cbffb0",
      padding: "0px 5px 2px 5px",
      fontWeight: "normal",
      textAlign: "center",
      border: "1px solid #178603",
      borderRadius: "3px",
    };

    const userIcon = (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M10 10C12.7614 10 15 7.76142 15 5C15 2.23858 12.7614 0 10 0C7.23858 0 5 2.23858 5 5C5 7.76142 7.23858 10 10 10Z" fill="#9C9C9C"></path><path d="M10 11.666C5.85977 11.6706 2.50461 15.0258 2.5 19.166C2.5 19.6262 2.87309 19.9993 3.33332 19.9993H16.6666C17.1269 19.9993 17.5 19.6262 17.5 19.166C17.4954 15.0258 14.1402 11.6706 10 11.666Z" fill="#9C9C9C"></path></svg>
    );

    const userAvatarImage = (
      <img
        className="img-fluid"
        style={{ borderRadius: "100%" }}
        src={
          userAvatar ||
          `${process.env.PUBLIC_URL}/images/user-thumbnail/user1.jpeg`
        }
        alt="User Thumbnail"
        onError={(e) => {
          e.target.src = `${process.env.PUBLIC_URL}/images/user-thumbnail/user1.jpeg`;
        }}
      />
    );

    return (
      <>
        <section className="bottom-sticky-footer">
          <div className="container">
            <ul>
              <li>
                <Link
                  to="/"
                  className={activePath === "/" ? "active" : ""}
                  onClick={() => this.setActive("/")}
                >
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <g clipPath="url(#clip0_768_7483)">
                      <path d="M18.1642 4.64901L12.3308 0.712344C10.915 -0.242656 9.085 -0.242656 7.66917 0.712344L1.83667 4.64901C0.686667 5.42401 0 6.71484 0 8.10318V15.8332C0 18.1307 1.86917 19.9998 4.16667 19.9998H15.8333C18.1308 19.9998 20 18.1307 20 15.8332V8.10318C20 6.71568 19.3133 5.42401 18.1642 4.64901ZM13.3333 13.3332C13.3333 14.2523 12.5858 14.9998 11.6667 14.9998H8.33333C7.41417 14.9998 6.66667 14.2523 6.66667 13.3332V9.99984C6.66667 9.08068 7.41417 8.33318 8.33333 8.33318H11.6667C12.5858 8.33318 13.3333 9.08068 13.3333 9.99984V13.3332ZM11.6667 9.99984L11.6683 13.3332H8.33333V9.99984H11.6667Z" fill="#9C9C9C" />
                    </g>
                    <defs>
                      <clipPath>
                        <rect width="20" height="20" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </Link>
              </li>
              <li>
                <Link
                  to="/categorias/fotografia"
                  className={activePath.startsWith("/categorias/") ? "active" : ""}
                  onClick={() => this.setActive("/categorias/")}
                >
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M15.5812 19.9993C18.0215 19.9993 19.9998 18.021 19.9998 15.5807C19.9998 13.1404 18.0215 11.1621 15.5812 11.1621C13.1409 11.1621 11.1626 13.1404 11.1626 15.5807C11.1626 18.021 13.1409 19.9993 15.5812 19.9993Z" fill="#9C9C9C" />
                    <path d="M6.27907 0H2.55814C1.14532 0 0 1.14532 0 2.55814V6.27907C0 7.69189 1.14532 8.83721 2.55814 8.83721H6.27907C7.69189 8.83721 8.83721 7.69189 8.83721 6.27907V2.55814C8.83721 1.14532 7.69189 0 6.27907 0Z" fill="#9C9C9C" />
                    <path d="M6.27907 11.1621H2.55814C1.14532 11.1621 0 12.3074 0 13.7202V17.4412C0 18.854 1.14532 19.9993 2.55814 19.9993H6.27907C7.69189 19.9993 8.83721 18.854 8.83721 17.4412V13.7202C8.83721 12.3074 7.69189 11.1621 6.27907 11.1621Z" fill="#9C9C9C" />
                    <path d="M17.4417 0H13.7207C12.3079 0 11.1626 1.14532 11.1626 2.55814V6.27907C11.1626 7.69189 12.3079 8.83721 13.7207 8.83721H17.4417C18.8545 8.83721 19.9998 7.69189 19.9998 6.27907V2.55814C19.9998 1.14532 18.8545 0 17.4417 0Z" fill="#9C9C9C" />
                  </svg>
                </Link>
              </li>
              <li>
                <Link
                  to="/cart"
                  className={activePath === "/cart" ? "active" : ""}
                  onClick={() => this.setActive("/cart")}
                >
                  <div className="cart-counter">{cartCount}</div>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <g clipPath="url(#clip0_768_5983)">
                      <path d="M18.9275 3.3975C18.6931 3.1162 18.3996 2.88996 18.0679 2.73485C17.7363 2.57973 17.3745 2.49955 17.0083 2.5H3.535L3.5 2.2075C3.42837 1.59951 3.13615 1.03894 2.67874 0.632065C2.22133 0.225186 1.63052 0.000284828 1.01833 0L0.833333 0C0.61232 0 0.400358 0.0877974 0.244078 0.244078C0.0877974 0.400358 0 0.61232 0 0.833333C0 1.05435 0.0877974 1.26631 0.244078 1.42259C0.400358 1.57887 0.61232 1.66667 0.833333 1.66667H1.01833C1.22244 1.66669 1.41945 1.74163 1.57198 1.87726C1.72451 2.0129 1.82195 2.19979 1.84583 2.4025L2.9925 12.1525C3.11154 13.1665 3.59873 14.1015 4.36159 14.78C5.12445 15.4585 6.10988 15.8334 7.13083 15.8333H15.8333C16.0543 15.8333 16.2663 15.7455 16.4226 15.5893C16.5789 15.433 16.6667 15.221 16.6667 15C16.6667 14.779 16.5789 14.567 16.4226 14.4107C16.2663 14.2545 16.0543 14.1667 15.8333 14.1667H7.13083C6.61505 14.1652 6.11233 14.0043 5.69161 13.7059C5.27089 13.4075 4.95276 12.9863 4.78083 12.5H14.7142C15.6911 12.5001 16.6369 12.1569 17.3865 11.5304C18.1361 10.9039 18.6417 10.0339 18.815 9.0725L19.4692 5.44417C19.5345 5.08417 19.5198 4.71422 19.4262 4.36053C19.3326 4.00684 19.1623 3.67806 18.9275 3.3975Z" fill="#9C9C9C" />
                      <path d="M5.83317 19.9993C6.75365 19.9993 7.49984 19.2532 7.49984 18.3327C7.49984 17.4122 6.75365 16.666 5.83317 16.666C4.9127 16.666 4.1665 17.4122 4.1665 18.3327C4.1665 19.2532 4.9127 19.9993 5.83317 19.9993Z" fill="#9C9C9C" />
                      <path d="M14.1667 19.9993C15.0871 19.9993 15.8333 19.2532 15.8333 18.3327C15.8333 17.4122 15.0871 16.666 14.1667 16.666C13.2462 16.666 12.5 17.4122 12.5 18.3327C12.5 19.2532 13.2462 19.9993 14.1667 19.9993Z" fill="#9C9C9C" />
                    </g>
                    <defs>
                      <clipPath>
                        <rect width="20" height="20" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </Link>
              </li>
              <li>
                {isLoggedIn ? (
                  <Link to="/profile" className={activePath === "/profile" ? "active" : ""}>
                    {userAvatarImage}
                  </Link>
                ) : (
                  <button onClick={this.open} className="btn-no-style">
                    {userIcon}
                  </button>
                )}
              </li>
            </ul>
          </div>
        </section>

        {/* Login Modal */}
        {/* Login Modal */}
        <Modal
          show={showModal}
          onHide={this.close}
          animation={true}
          className="mobile-modal-dialog"
        >
          <Modal.Body>
            <div className="popupbg mobile-popupbg">
              <button type="button" className="close" onClick={this.close}>
                &times;
              </button>

              <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12 right-form-area mobile-logo-login">
                <div className="right-form-logo">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/popup-logo.png`}
                    title="logo"
                    alt="logo"
                  />
                </div>
                <h2>Iniciar sesión</h2>
                <h3>Obtenga acceso sus cuentas y pedidos</h3>

                <div className={`form-wrap ${apiErrorMessage ? "" : "hidden"}`}>
                  <label style={apiError}>{apiErrorMessage}</label>
                </div>
                <div
                  className={`form-wrap ${apiSuccessMessage ? "" : "hidden"}`}
                >
                  <label style={apiSuccess}>{apiSuccessMessage}</label>
                </div>
                <form
                  className="login_form"
                  onSubmit={this.handleOnLoginSubmit}
                >
                  <div className="form-wrap">
                    <div className="input-wrap">
                      <label className="input-label">Enter email ID</label>
                      <input
                        className={
                          "input-text" +
                          (loginSubmitted && !username ? " has-error" : "")
                        }
                        type="email"
                        name="username"
                        value={username}
                        onChange={this.handleOnLoginInputChange}
                        placeholder="Enter Your Email"
                        maxLength="70"
                      />
                    </div>
                    <span style={{ color: "red" }}>{errors["username"]}</span>
                  </div>
                  <div className="form-wrap">
                    <div className="input-wrap">
                      <label className="input-label">Password</label>
                      <input
                        className={
                          "input-text" +
                          (loginSubmitted && !password ? " has-error" : "")
                        }
                        type="password"
                        name="password"
                        value={password}
                        onChange={this.handleOnLoginInputChange}
                        maxLength="50"
                        placeholder="Enter Your Password"
                      />
                    </div>
                    <span style={{ color: "red" }}>{errors["password"]}</span>
                  </div>
                  <div className="signup-btn-wrap">
                    <button
                      className="btn"
                      value="Login"
                      type="submit"
                      disabled={loginFormButtonDisabled}
                    >
                      <i
                        className={`fa fa-refresh fa-spin ${loginLoader}`}
                        style={{ fontSize: "17px" }}
                      ></i>{" "}
                      Iniciar sesión
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </Modal.Body>
        </Modal>
      </>
    );
  }
}

const mapStateToProps = (state) => ({
  cartCount: state.cart.cartCount,
  userAvatar: state?.user?.user?.avatar,
  isLoggedIn: state.user.isLoggedIn,
  cartItems: state.cart.cartData,
  isError: state.user.isError,
  errorMessage: state.user.errorMessage,
});

const mapDispatchToProps = (dispatch) => ({
  logIn: (username, password, cartItems) =>
    dispatch(login2(username, password, cartItems)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(MobileFooterSticky));