import React from "react";
import { Multiselect } from "multiselect-react-dropdown";

const MobileProductFilterSection = ({
  isFilterActive,
  setIsFilterActive,
  brandOptions,
  selectedBrands,
  onBrandSelect,
  onBrandRemove,
  isStockSelected,
  onStockSelect,
  priceRange,
  handlePriceRangeChange,
  applyFilters,
  resetFilters,
  handleSortChange
}) => {
  const stockOptions = [{ name: "In Stock", id: 1 }];
  
  // Create references for the multiselect components to reset them
  const brandMultiselectRef = React.useRef();
  const stockMultiselectRef = React.useRef();
  
  // Helper function to enforce numbers only
  const handlePriceInputChange = (e) => {
    const re = /^[0-9\b]+$/;
    if (e.target.value === "" || re.test(e.target.value)) {
      const { name, value } = e.target;
      handlePriceRangeChange(name, value);
    }
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    applyFilters(); // Apply all current filters
    setIsFilterActive(false); // Close the modal
  };

  const handleFilterByType = (filterType, e) => {
    e.preventDefault();
    applyFilters(filterType);
  };

  const handleReset = (e) => {
    e.preventDefault();
    
    // Reset multiselect components
    if (brandMultiselectRef.current) {
      brandMultiselectRef.current.resetSelectedValues();
    }
    
    if (stockMultiselectRef.current) {
      stockMultiselectRef.current.resetSelectedValues();
    }
    
    resetFilters();
  };

  return (
    <form onSubmit={handleFormSubmit} className="form-container p-0">
      <div className="filter-header">
        <h6 className="m-0">Filters</h6>
        <button
          className="ml-auto"
          onClick={(e) => {
            e.preventDefault();
            setIsFilterActive(!isFilterActive);
          }}
        >
          <img
            src={`${process.env.PUBLIC_URL}/images/icon__times.svg`}
            alt="Close"
          />
        </button>
      </div>
      <div className="p-3">
        <div className="mb-4">
          <h6 className="m-0">Seleccionar</h6>
          <Multiselect
            options={brandOptions || []}
            selectedValues={selectedBrands.map(brandId => brandOptions.find(opt => opt.id === brandId))}
            showCheckbox={true}
            placeholder="Seleccionar Marca"
            onSelect={onBrandSelect}
            onRemove={onBrandRemove}
            displayValue="name"
            ref={brandMultiselectRef}
          />
          <button 
            type="button" 
            className="btn btn-sm mt-2"
            onClick={(e) => handleFilterByType("brand", e)}
          >
            Filtrar Por Marca
          </button>

          <Multiselect
            options={stockOptions}
            selectedValues={isStockSelected ? stockOptions : []}
            showCheckbox={true}
            placeholder="Seleccionar en-stock"
            onSelect={onStockSelect}
            onRemove={() => onStockSelect([], null)}
            displayValue="name"
            ref={stockMultiselectRef}
          />
          <button 
            type="button" 
            className="btn btn-sm mt-2"
            onClick={(e) => handleFilterByType("stock", e)}
          >
            Filtrar Productos
          </button>
        </div>
        
        <div className="mb-4">
          <h6 className="m-0">Seleccionar Precio</h6>
          <div className="form-groups">
            <span>$ </span>
            <input
              type="text"
              name="from"
              value={priceRange.from || ""}
              onChange={handlePriceInputChange}
              maxLength="6"
              placeholder="Desde"
            />
          </div>
          <div className="form-groups">
            <span>$ </span>
            <input
              type="text"
              name="to"
              value={priceRange.to || ""}
              onChange={handlePriceInputChange}
              maxLength="6"
              placeholder="A"
            />
          </div>
          <button 
            type="button" 
            className="btn btn-sm mt-2"
            onClick={(e) => handleFilterByType("price", e)}
          >
            Filtrar Por Precio
          </button>
        </div>
        
        <div className="m-0">
          <h6 className="m-0">Ordenar Por</h6>
          <select onChange={handleSortChange}>
            <option value="">Seleccionar Precio Filtrar</option>
            <option value="lth">Price: Low to High</option>
            <option value="htl">Price: High to Low</option>
          </select>
        </div>
        
        <div className="filter-buttons mt-4 d-flex" style={{gap: "1rem"}}>
          <button 
            type="button"
            className="btn w-50"
            style={{ background: "#0075B9", color: "#fff", borderRadius: "30px" }}
            onClick={handleReset}
          >
            Reset
          </button>
          <button 
            type="submit"
            className="btn w-50"
            style={{ 
              background: "#0075B9", 
              color: "#fff", 
              borderRadius: "30px" 
            }}
          >
            Apply Filters
          </button>
        </div>
      </div>
    </form>
  );
};

export default MobileProductFilterSection;