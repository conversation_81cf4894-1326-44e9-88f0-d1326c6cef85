// ######################################
// Header Page (Mobile)
// ######################################

// Secondary Header
.header {
  &--mobile {
    @media (min-width: 768px) {
      & {
        display: none !important;
      }
    }
  }

  &--secondary {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    border-bottom: solid 1px $header-border;
    position: relative;

    .button {
      width: 36px;
      height: 36px;
      border-radius: 6px;
      background: $header-button-background;
      transition: all 0.3s ease;
      border: none;
      outline: none;
      box-shadow: none;

      svg path {
        stroke: $nav-text;
        transition: all 0.3s ease;
      }

      &:hover,
      &.active {
        background: $nav-hover;

        svg path {
          stroke: $white;
        }
      }

      &-group {
        display: flex;
        align-items: center;

        button {
          &:not(:first-child) {
            margin-left: 1rem;
          }
        }
      }
    }

    .search {
      position: absolute;
      top: calc(100% + 1px);
      left: 0;
      right: 0;
      padding: 1rem;
      background: $white;
      border-bottom: solid 1px $header-border;
      z-index: 1;
      transform: translateX(100vw);
      transition: all 0.3s ease;

      .search_box_area {
        margin: 0;
      }

      &.visible {
        transform: translateX(0);
      }
    }
  }
}
