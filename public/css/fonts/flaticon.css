	/*
  	Flaticon icon font: Flaticon
  	Creation date: 21/10/2016 08:38
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
        font-size: 20px;
font-style: normal;
margin-left: 20px;
}

.flaticon-arrows:before { content: "\f100"; }
.flaticon-commerce:before { content: "\f101"; }
.flaticon-graphic:before { content: "\f102"; }
.flaticon-graphic-1:before { content: "\f103"; }
.flaticon-handshake:before { content: "\f104"; }
.flaticon-headphones:before { content: "\f105"; }
.flaticon-interface:before { content: "\f106"; }
.flaticon-layers:before { content: "\f107"; }
.flaticon-light-bulb:before { content: "\f108"; }
.flaticon-mail:before { content: "\f109"; }
.flaticon-share:before { content: "\f10a"; }
.flaticon-target:before { content: "\f10b"; }
.flaticon-text-on-paper-sheet-sketch:before { content: "\f10c"; }