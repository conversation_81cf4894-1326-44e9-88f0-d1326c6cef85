// ######################################
// Category Page (Mobile)
// ######################################

$header-button-background: #ecf2f8;
$nav-background: #dfe7f4;
$header-border: $nav-background;
$nav-text: #131921;
$nav-hover: #0075b9;
$accordion-header-border: $nav-background;
$item-color: #3c525d;

// Category Header

.mobile {
  &.category-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    border-bottom: solid 1px $header-border;
    position: relative;

    .button {
      width: 36px;
      height: 36px;
      border-radius: 6px;
      background: $header-button-background;
      transition: all 0.3s ease;
      border: none;
      outline: none;
      box-shadow: none;

      svg path {
        stroke: $nav-text;
        transition: all 0.3s ease;
      }

      &:hover,
      &.active {
        background: $nav-hover;

        svg path {
          stroke: $white;
        }
      }

      &-group {
        display: flex;
        align-items: center;

        button {
          &:not(:first-child) {
            margin-left: 1rem;
          }
        }
      }
    }

    .search {
      position: absolute;
      top: calc(100% + 1px);
      left: 0;
      right: 0;
      padding: 1rem;
      background: $white;
      border-bottom: solid 1px $header-border;
      z-index: 1;
      transform: translateX(100vw);
      transition: all 0.3s ease;

      .search_box_area {
        margin: 0;
      }

      &.visible {
        transform: translateX(0);
      }
    }

    .filter {
      position: fixed;
      top: 25%;
      left: 0;
      right: 0;
      bottom: 59px;
      background: $white;
      border-bottom: solid 1px $header-border;
      z-index: 3;
      transform: translateY(100vh);
      transition: all 0.3s ease;
      overflow-x: hidden;
      overflow-y: auto;

      &.visible {
        transform: translateY(0);
      }

      input[type="text"],
      select {
        margin: 12px 0 0;
      }

      &-header {
        padding: 12px 16px;
        border-bottom: solid 1px $header-border;
        display: flex;
        align-items: center;

        button {
          background: none;
          border: none;
          box-shadow: none;
          outline: none;
          height: 20px;
          width: 20px;
          padding: 0;

          img {
            display: block;
          }
        }
      }

      &-backdrop {
        background: #061d2a;
        opacity: 0.7;
        position: fixed;
        inset: 0;
        z-index: 2;
      }
    }
  }
}

// Category Main

.mobile {
  &.category-main {
    display: flex;
    color: $nav-text;
    font-size: 13px;
    line-height: 19px;

    // Category Sidebar
    .category {
      &-sidebar {
        width: 90px;
        flex-shrink: 0;
        background: $nav-background;

        ul {
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            text-align: center;

            &:not(:first-child) {
              border-top: solid 1px $white;
            }

            a {
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 20px 10px;
              transition: all 0.3s ease;
              color: $nav-text;

              &:hover,
              &.active {
                background: $nav-hover;
                color: $white;
              }

              & > img {
                height: 24px;
                width: auto;
                margin-bottom: 4px;
              }

              span {
                display: block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
              }
            }
          }
        }
      }

      // Category Listing
      &-listing {
        width: calc(100vw - 90px);
        flex-grow: 1;

        // Sub Category Accordion
        .accordion {
          &-header {
            border-bottom: solid 1px $accordion-header-border;

            & > .btn {
              border-radius: 0px;
              display: flex;
              align-items: center;
              padding: 12px 16px;
              font-size: 14px;
              line-height: 20px;
              font-weight: 500;
              color: $black;
              text-decoration: none;
              text-align: left;
              text-transform: capitalize;
              width: 100%;

              & > span {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                padding-right: 16px;
                width: calc(100% - 14px);
              }

              &:after {
                content: "";
                width: 14px;
                height: 8px;
                background: url(../images/icon__accordion-arrow.svg) no-repeat 0
                  0;
                background-size: 100%;
                flex-shrink: 0;
                transition: all 0.3s ease;
              }
            }

            &.active {
              & > .btn {
                &:after {
                  transform: rotate(180deg);
                }
              }
            }
          }

          &-item {
            background: $white;

            &:not(:first-child) {
              border-top: solid 5px $nav-background;
            }
          }

          &-body {
            padding: 8px 16px;
          }
        }

        // Single Product

        .col-6 {
          margin: 8px 0;

          &:nth-child(odd) {
            padding-right: 8px;
          }
          &:nth-child(even) {
            padding-left: 8px;
          }
        }

        .item {
          border: solid 1px $nav-background;
          border-radius: 14px;
          padding: 5px;
          min-height: 100%;
          display: flex;
          flex-direction: column;

          &__pic {
            background: #2d5ba526;
            border-radius: 14px;
            margin-bottom: 8px;
            flex-shrink: 0;
            display: flex;
          }

          &__info {
            padding: 0 5px 5px;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
          }

          &__name {
            font-size: 9px;
            line-height: 12px;
            color: $item-color;
            margin: 0;

            a {
              color: $item-color;

              &:hover {
                color: $nav-hover;
              }
            }
          }

          &__review {
            font-size: 8px;
            line-height: 12px;
            color: $item-color;
            display: flex;
            align-items: center;
            margin-top: auto;
            padding-top: 8px;

            img {
              height: 8px;
              width: auto;
              margin-right: 4px;
            }
          }

          &__price {
            font-size: 11px;
            line-height: 13px;
            font-weight: 600;
            color: $item-color;
            margin: 0;

            .old {
              font-size: 9px;
              line-height: 12px;
              text-decoration: line-through;
              font-weight: 400;
              display: block;
            }
          }

          &__action {
            margin-left: auto;
            font-size: 9px;
            line-height: 11px;
            color: $item-color;
            font-weight: 500;
            text-align: right;

            a {
              color: $item-color;
            }

            button {
              background: none;
              padding: 0;
              margin: 0;

              img {
                height: 14px;
                width: auto;
                display: block;
              }
            }
          }
        }
      }
    }
  }
}

.feedback-btn {
  display: none !important;
}
