$accordion-header-border: #dfe7f4;

.dashboard-container {
  background: #fff;
  padding: 33px 0 30px;

  @media (max-width: 767.98px) {
    & {
      background: #f0faff;
      padding: 16px;

      &-mobile {
        min-height: calc(100vh - 59px);

        h5 {
          font-size: 18px;
          line-height: 24px;
          border-bottom: solid 1px #dfe7f4;
          padding: 0 0 15px;
        }

        p {
          color: #7d899d;
        }

        .block {
          background: #fff;
          border: solid 1px #dfe7f4;
          border-radius: 10px;
          padding: 20px 16px;

          .btn {
            width: 100%;
          }

          .breadcamp,
          .filter,
          .dasboard-title {
            display: none !important;
          }

          .tabname {
            border: none;
            padding-top: 0;
            font-size: 16px;
            line-height: 22px;
            color: $black;
          }

          .block-title-2 {
            color: $black;
          }

          .table-responsive {
            border-radius: 0;
            margin: 0;
          }

          .dashboard-right-panel {
            .submit-btn {
              margin-bottom: 10px;
            }

            .panel {
              margin: 0;
              padding: 1rem;
              border: solid 1px #dfe7f4;

              .panel-heading {
                .panel-title {
                  font-size: 16px;
                  line-height: 22px;
                  color: $black;
                  font-weight: 500;
                }
              }
            }

            .table {
              background: none;
              border-radius: 0;

              tbody {
                td {
                  padding: 8px !important;
                }
              }

              .thead-light {
                th {
                  padding: 8px !important;
                  font-size: 12px;
                  line-height: 20px;
                  font-weight: 500;
                }
              }
            }
          }

          .line-footer {
            display: none;
          }

          .dashboard-container {
            background: none;
            padding: 0;

            .container,
            .row,
            [class*="col-"] {
              padding: 0;
              margin: 0;
            }
          }

          .pager {
            margin: 0;
            gap: 0;

            li {
              &:not(:first-child) {
                margin-top: 10px;
              }
            }
          }
        }

        .accordion {
          &-header {
            & > .btn {
              border-radius: 0px;
              display: flex;
              align-items: center;
              padding: 16px 0;
              font-size: 14px;
              line-height: 20px;
              font-weight: 500;
              color: $black;
              text-decoration: none;
              text-align: left;
              text-transform: capitalize;
              width: 100%;

              & > span {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                padding-right: 16px;
                width: calc(100% - 14px);
                display: flex;
                align-items: center;

                img {
                  margin-right: 1rem;
                }
              }

              &:after {
                content: "";
                width: 14px;
                height: 8px;
                background: url(../images/icon__accordion-arrow.svg) no-repeat 0
                  0;
                background-size: 100%;
                flex-shrink: 0;
                transition: all 0.3s ease;
              }
            }

            &.active {
              & > .btn {
                &:after {
                  transform: rotate(180deg);
                }
              }
            }
          }

          &-body {
            padding: 8px 16px;
          }
        }
      }
    }
  }
}
.dasboard-title {
  font-size: 35px;
  line-height: 27px;
  color: #3c525d;
  margin-bottom: 40px;
  text-align: center;
  font-weight: 600;
  text-transform: capitalize;
}
.navigation {
  width: 100%;
  background-color: #0075b9;
  border-radius: 10px;
  padding: 10px 0;
}

// .navigation h2 {
//     width: 100%;
//     font-size: 18px;
//     font-weight: 700;
//     padding: 10px;
//     margin: 0px;
//     color: #fff;
//     text-align: left;
//     border-bottom: 1px solid #043f5152;
// }

.mainmenu,
.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* make ALL links (main and submenu) have padding and background color */
.mainmenu li {
  a {
    position: relative;
    display: block;
    text-decoration: none !important;
    padding: 0 30px;
    @media (max-width: 1180px) {
      padding: 0 15px;
    }
    &:before {
      position: absolute;
      content: "";
      width: 3px;
      height: 25px;
      border-radius: 0 2px 2px 0;
      background-color: #de1d1d;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      transition: opacity 0.3s;
      opacity: 0;
    }
    .d-flex {
      padding: 20px 0;
      border-bottom: 1px solid #0d86cc;
    }
    span {
      font-size: 16px;
      font-weight: 500;
      color: #fff;
      @media (max-width: 992px) {
        width: 100%;
        text-align: center;
        font-size: 14px;
      }
      img {
        margin-right: 15px;
        @media (max-width: 1180px) {
          margin-right: 7px;
        }
        @media (max-width: 992px) {
          display: block;
          margin: 0 auto 15px;
        }
      }
    }
    svg {
      transition: opacity 0.3s;
      opacity: 0;
      @media (max-width: 992px) {
        display: none;
      }
    }
    &:hover {
      &:before {
        opacity: 1;
      }
      svg {
        opacity: 1;
      }
    }
    &.active {
      &:before {
        opacity: 1;
      }
      svg {
        opacity: 1;
      }
    }
  }
  &.active {
    a {
      &:before {
        opacity: 1;
      }
      svg {
        opacity: 1;
      }
    }
  }
  &:last-child {
    a {
      .d-flex {
        border-bottom: none;
      }
    }
  }
}
.dashboard-right-panel {
  .tabname {
    font-size: 25px;
    line-height: 27px;
    color: #3c525d;
    font-weight: 600;
    border-top: 1px solid #cce3f1;
    padding-top: 25px;
  }
  p {
    font-size: 15px;
    line-height: 27px;
    color: #3c525d;
    &.name-bottom-margin {
      margin-bottom: 40px;
    }
  }
  .userInfo {
    margin-top: 20px;
  }
  .form-group {
    .form-control {
      color: #76868e;
    }
  }
  .margin-top-btn {
    margin-top: 15px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    @media (max-width: 767px) {
      flex-direction: column;
    }
  }
  .submit-btn {
    margin-right: 19px;
    @media (max-width: 767px) {
      margin: 0 0 25px;
    }
  }
  .btn {
    background-color: #0075b9 !important;
    color: #fff !important;
    font-size: 14px;
    font-weight: 600;
    border-radius: 44px !important;
    padding: 11px 20px;
    border-color: #0075b9 !important;
    @media (max-width: 767px) {
      width: 100%;
    }
    &:hover {
      background-color: #0d86cc !important;
    }
    img {
      margin-left: 8px;
    }
  }

  .panel {
    background-color: #f0faff;
    border-radius: 16px;
    padding: 30px;
    border: none;
    box-shadow: none;
    margin-top: 24px;
    .panel-heading {
      padding: 0 0 10px;
      border: none;
      background-color: transparent;
      .panel-title {
        font-size: 20px;
        line-height: 27px;
        color: #3c525d;
        font-weight: 600;
        margin: 0;
      }
    }
    .panel-body {
      padding: 0;
      .tittle-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 15px;
        border-bottom: 1px solid #cfd7e5;
        h4 {
          font-size: 14px;
          line-height: 27px;
          color: #3c525d;
          font-weight: 600;
          margin: 0;
        }
        .edit-btn {
          text-decoration: none !important;
          color: #0075b9;
          font-size: 14px;
          line-height: 27px;
          font-weight: 600;
          img {
            margin-right: 8px;
          }
        }
      }
      .address-box-output {
        margin-top: 12px;
        p {
          font-size: 12px;
          line-height: 27px;
          margin: 0;
        }
      }
    }
  }

  .table {
    background-color: #f0faff;
    border-radius: 10px;
    overflow: hidden;
    .thead-light {
      padding: 0 20px;
      th {
        background-color: #0075b9;
        border: none;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        padding: 12px 25px !important;
      }
    }
    tbody {
      td {
        position: relative;
        font-size: 12px;
        padding: 12px 25px !important;
        color: #3c525d;
        border-color: #dbeff9;
        vertical-align: middle;
        &:first-child {
          &:before {
            position: absolute;
            content: "";
            width: 22px;
            height: 1px;
            background-color: #f0faff;
            left: 0;
            bottom: -1px;
          }
        }
        &:last-child {
          &:before {
            position: absolute;
            content: "";
            width: 22px;
            height: 1px;
            background-color: #f0faff;
            right: 0;
            bottom: -1px;
          }
        }
        .label-warning {
          color: #de9f03;
          font-weight: 400;
          background-color: #f0f8f8;
          border: 1px solid #efd27f;
          padding: 7px 15px;
          border-radius: 30px;
          font-size: 12px;
        }
        .label-success {
          color: #24c22a;
          font-weight: 400;
          background-color: #ecf9fb;
          border: 1px solid #88de93;
          padding: 7px 15px;
          border-radius: 30px;
          font-size: 12px;
        }
        .label-danger {
          color: #de1d1d;
          font-weight: 400;
          background-color: #efeff4;
          border: 1px solid #de1d1d;
          padding: 7px 15px;
          border-radius: 30px;
          font-size: 12px;
        }
        .dwnld-btn {
          text-decoration: none !important;
          color: #3c525d;
          font-size: 12px;
          img {
            margin-right: 7px;
          }
        }
        .view-order {
          color: #3c525d;
          text-decoration: none !important;
          font-size: 12px;
          img {
            margin-right: 10px;
          }
        }
      }
    }
  }
}
.dash-button {
  width: 100%;
  display: inline-block !important;
  text-align: left;
  .btn {
    background-color: #0075b9;
    color: #fff !important;
    font-size: 14px;
    font-weight: 600;
    border-radius: 44px !important;
    padding: 11px 20px;
    border-color: #0075b9;
    &:hover {
      background-color: #0d86cc;
    }
    img {
      margin-left: 8px;
    }
  }
}

.mobile-hamburger {
  color: #0075b9;
  font-size: 40px;
  position: absolute;
  top: 16px;
  z-index: 1;
  @media (max-width: 767px) {
    display: none;
  }
}

.dropdown-list-menu::after {
  content: "+";
  font-size: 24px;
  position: absolute;
  right: 0px;
  color: #3c525d;
  top: 12px;
  font-weight: 200;
}

/* add hover behaviour */

.mainmenu .menuactive {
  //   background-color: #20bced;
  //   color: #fff !important;
}
/* when hovering over a .mainmenu item,
  display the submenu inside it.
  we're changing the submenu's max-height from 0 to 200px;
*/

.mainmenu li:hover .submenu {
  display: block;
  max-height: 500px;
  overflow: auto;
}

.mainmenu li:hover .submenu::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

.mainmenu li:hover .submenu::-webkit-scrollbar {
  width: 2px;
  background-color: #f5f5f5;
}

.mainmenu li:hover .submenu::-webkit-scrollbar-thumb {
  background-color: #20bced;
  border: 2px solid #8ad4eb;
}

/*
  we now overwrite the background-color for .submenu links only.
  CSS reads down the page, so code at the bottom will overwrite the code at the top.
*/

/* this is the initial state of all submenus.
  we set it to max-height: 0, and hide the overflowed content.
*/

.arrow {
  border: solid white;
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 3px;
}

.sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 9999999999;
  top: 0;
  left: 0;
  background-color: #f0faff;
  overflow-y: hidden;
  transition: 0.5s;
  padding-top: 0px;
  .navigation {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 0;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    /* width */
    &::-webkit-scrollbar {
      width: 5px;
    }
    &::-webkit-scrollbar-track {
      background: #0075b9;
    }
    &::-webkit-scrollbar-thumb {
      background: #20bced;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #1a1a1a;
    }
    @media (max-width: 767px) {
      h2 {
        font-size: 18px;
        padding: 5px 0 15px 15px;
        text-indent: 0;
        font-weight: 500;
        color: #fff;
        width: 100%;
      }
    }
  }
  .mainmenu {
    width: 100%;
    li {
      position: relative;
      &:after {
        @media (max-width: 767px) {
          color: #fff;
          top: 50%;
          right: 13px;
          transform: translateY(-50%);
        }
      }
      a {
        padding: 15px 5px 15px 30px;
        font-size: 20px;
        font-weight: 500;
        @media (max-width: 767px) {
          padding: 8px 30px 8px 15px;
          font-size: 15px;
          font-weight: 400;
        }
      }
      .submenu {
        @media (max-width: 767px) {
          position: relative;
          margin-left: 10%;
          width: 88%;
          background: #20bced;
          a {
            padding: 9px 8px 9px 25px;
            font-size: 13px;
          }
        }
      }
    }
  }
}

.sidenav a {
  padding: 8px 8px 8px 32px;
  text-decoration: none;
  font-size: 15px;
  color: #fff;
  display: block;
  transition: 0.3s;
}

.sidenav a:hover {
  color: #fff;
}

/* The popup form - hidden by default */
.form-popup {
  position: fixed;
  top: 12px;
  left: 0;
  width: 90%;
  border: 3px solid #f1f1f1;
  background: #fff;
  z-index: 999999999999;
  margin: 0 5%;
  border-radius: 5px;
  height: calc(100%-12px);
  overflow-x: hidden;
  overflow-y: auto;
}
.hamburg-sideicon {
  font-size: 40px;
  cursor: pointer;
  margin: 0px;
  float: left;
  color: #0075b9;
}

.dashboard-container--mobile-order {
  .col-lg-9.col-md-9.col-sm-12.col-xs-12 {
    @media (max-width: 767px) {
      padding: 0;
    }
    .col-lg-6.col-md-6.col-sm-12.col-xs-12 {
      @media (max-width: 767px) {
        padding: 0;
      }
    }
    .col-lg-3.col-md-3.col-sm-12.col-xs-12 {
      @media (max-width: 767px) {
        padding: 0;
      }
    }
  }
  .section-title-inner {
    @media (max-width: 767px) {
      margin: 0 0 20px;
    }
  }
  .pad-left-0 {
    @media (max-width: 767px) {
      padding-left: 0;
    }
  }
  .pad-right-0 {
    @media (max-width: 767px) {
      padding-right: 0;
    }
  }
  .value-tr {
    td {
      @media (max-width: 767px) {
        text-align: left !important;
        padding: 10px 0 !important;
      }
      &:last-child {
        text-align: right !important;
      }
    }
  }
  .thead-light {
    th {
      @media (max-width: 767px) {
        color: #fff !important;
        background: #0075b9 !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
        font-size: 13px !important;
      }
    }
  }
  .table {
    td {
      @media (max-width: 767px) {
        padding: 10px 10px !important;
      }
    }
    tr {
      &:first-child {
        td {
          &:nth-child(3) {
            text-align: center;
          }
        }
      }
    }
  }
  .block-title-2 {
    @media (max-width: 767px) {
      font-size: 16px !important;
      font-weight: 400;
    }
  }
}
