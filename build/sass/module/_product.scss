// BREADCUM START
.breadcamp {
  width: 100%;
  font-size: 12px;
  @media (max-width: 1024px) {
    margin-top: 12px;
  }
  .container {
    border-top: 1px solid #daddde;
    padding: 15px 0 0;
    @media (max-width: 1024px) {
      padding: 15px 15px 0;
    }
  }
  a {
    font-size: 12px;
    color: #47545b;
    text-decoration: none;
    &:hover {
      color: #0075b9;
      text-decoration: none;
    }
  }
  .text-blue {
    color: #0075b9;
  }
  .separator {
    margin: 0 5px;
  }
  .bread-product-name {
    color: #0075b9;
  }
  &__catagory {
    .container {
      padding: 0 0 15px;
      border-top: none;
      border-bottom: 1px solid #daddde;
      @media only screen and (max-width: 767px) {
        padding: 0 15px 15px;
      }
    }
  }
}
// BREADCUM ENDS

/*------- NEW LISTING CONTENT -----------*/
.product-view-container {
  margin-top: 5px;
  padding: 30px 0;
  // PRODUCT SLIDER START
  .exzoom_img_ul {
    position: relative;
    .carousel-root {
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      @media (max-width: 992px) {
        display: block;
      }
      .carousel {
        display: inline-flex;
        width: auto;
        @media (max-width: 992px) {
          display: block;
        }
        .carousel-status {
          text-shadow: none;
          left: 0;
          bottom: 0;
          top: inherit;
          margin: auto;
          text-align: center;
        }
      }
      .thumbs-wrapper {
        width: 132px;
        margin: 0 20px 0 0;
        max-height: 556px;
        padding-right: 10px;
        overflow-y: scroll;
        scrollbar-color: #dfe6f1 #f9f9f9;
        scrollbar-width: thin;
        /* width */
        &::-webkit-scrollbar {
          width: 2px;
        }

        /* Track */
        &::-webkit-scrollbar-track {
          background: #f9f9f9;
        }

        /* Handle */
        &::-webkit-scrollbar-thumb {
          background: #dfe6f1;
        }

        /* Handle on hover */
        &::-webkit-scrollbar-thumb:hover {
          background: #0075b9;
        }
        &::-webkit-scrollbar {
          width: 2px;
          background-color: #f9f9f9;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #dfe6f1;
        }
        @media (max-width: 992px) {
          width: 100%;
          display: block;
          margin: 10px 0 20px;
          padding: 0;
          max-height: auto;
        }
        .thumbs {
          margin: 0;
          display: flex;
          flex-direction: column;
          transform: none !important;
          @media (max-width: 992px) {
            flex-direction: row;
            transform: translate3d(0, 0, 0) !important;
            overflow: scroll;
            white-space: inherit;
          }
        }
        .thumb {
          width: 98% !important;
          height: 122px;
          border-radius: 30px;
          background-color: #dfe6f1;
          margin: 2px auto 17px;
          border: 5px solid #dfe6f1;
          outline: 1px solid #dfe6f1;
          padding: 15px;

          &.selected {
            border: 5px solid #fff;
            outline: 1px solid #96add2;
          }
          &:hover {
            border: 5px solid #fff;
            outline: 1px solid #96add2;
          }
          @media (max-width: 992px) {
            width: 80px !important;
            flex: 0 0 80px;
            height: 80px;
            border-radius: 6px;
            margin: 0 5px;
          }
        }
      }

      .carousel-slider {
        .slide {
          border-radius: 30px;
          background-color: #dfe6f1;
          padding: 20px;
          min-height: 556px;
          display: flex;
          align-items: center;
          justify-content: center;
          @media (max-width: 992px) {
            border-radius: 6px;
            padding: 10px;
            min-height: auto;
            display: block;
          }
        }
      }
    }
    .control-dots {
      display: none;
    }
  }
  // PRODUCT SLIDER ENDS

  // PRODUCT RIGHT TEXT START
  /* =================== */
  /* Details page css */
  .details-container {
  }
  .details-container h1 {
    font-size: 19px;
    margin-top: 10px;
    float: left;
    width: 100%;
  }
  .details-container h2 {
    font-size: 35px;
    color: #3c525d;
    line-height: normal;
    font-weight: 600;
    margin: 0px 0 0px 0;
    text-transform: capitalize;
    @media (max-width: 1024px) {
      font-size: 28px;
    }
    @media (max-width: 767px) {
      font-size: 22px;
    }
  }
  .details-container h3 {
    font-size: 20px;
    color: #3c525d;
    line-height: 27px;
    font-weight: 400;
    margin: 0px 0 0px 0;
    text-transform: capitalize;
    @media (max-width: 767px) {
      font-size: 16px;
      line-height: 22px;
    }
  }
  .review-rating {
    display: flex;
    gap: 10px;
    margin: 20px 0 30px;
    padding: 0 0 20px;
    border-bottom: 1px solid #cfd7e5;
    width: 100%;
    @media (max-width: 767px) {
      margin: 0 0 15px;
      padding: 0 0 15px;
    }
    .star {
      display: inline-flex;
      .staricon {
        display: inline-block;
        font: normal normal normal 14px / 1 FontAwesome;
        font-size: inherit;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
        color: #f8ba22;
        font-size: 20px;
        margin: 0 5px 0 0;
        &:before {
          content: "\f005";
        }
      }
    }
    p {
      font-size: 14px;
      line-height: 27px;
      color: #3c525d;
      margin: 0;
    }
  }
  //   .details-container p {
  //     font-size: 15px;
  //     color: #20bced;
  //     line-height: 24px;
  //     font-weight: 400;
  //     margin: 0px 0 20px 0;
  //     text-align: left;
  //     text-transform: capitalize;
  //     float: left;
  //     width: 100%;
  //   }

  .details-container .link {
    float: left;
    width: 100%;
    margin: 20px 0;
    text-align: left;
    color: #cfd7e5;
  }

  .details-container .link a {
    color: #0075b9;
    text-decoration: none;
  }
  .details-container .link a:hover {
    color: #0075b9;
    text-decoration: none;
  }

  .details-container p {
    color: #3c525d;
    text-decoration: none;
    font-size: 15px;
    line-height: 23px;
  }

  .details-container p a {
    color: #333;
    text-decoration: none;
  }
  .details-container p a:hover {
    color: #1286ab;
    text-decoration: none;
  }

  .details-container .price {
    font-size: 55px;
    line-height: normal;
    font-weight: 400;
    margin: 0px 0 0px 0;
    text-transform: capitalize;
    color: #3c525d;
  }

  .details-container .qty {
    font-size: 20px;
    color: #3c525d;
    line-height: 27px;
    font-weight: 400;
    margin: 0 20px 0 0;
    text-align: center;
    border: 1px solid #cfd7e5;
    border-radius: 50px;
    max-width: 152px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    .count-btn {
      position: relative;
      color: #3c525d;
      font-size: 26px;
      text-decoration: none;
      top: 1px;
    }
  }

  .details-container .qty input[type="text"] {
    background: #fff;
    border: none;
    height: 40px;
    font-size: 20px;
    width: 80px;
    color: #3c525d;
    text-align: center;
  }

  .details-container button {
    border: 1px solid #cfd7e5 !important;
    outline: 0;
    padding: 9px 10px;
    color: #0075b9;
    min-width: 210px;
    text-align: center;
    cursor: pointer;
    width: auto !important;
    font-weight: 500;
    font-size: 16px;
    border-radius: 50px;
    background: transparent;
    img {
      max-width: 15px;
    }
  }

  pre {
    margin-top: 1rem;
    margin-bottom: 1rem;
    overflow: auto;
  }

  .upsellproducts .slick-track {
    /*     width: 100% !important; */
    opacity: 1;
    transform: translate3d(0px, 0px, 0px);
    margin: 0 -4px !important;
  }
  .upsellproducts .slick-initialized .slick-slide {
    display: block;
    width: 278px !important;
  }

  @-webkit-keyframes blinker {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  .blink {
    text-decoration: blink;
    -webkit-animation-name: blinker;
    -webkit-animation-duration: 0.6s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-direction: alternate;
  }

  .details-container .whatsapp {
    border: none;
    outline: 0;
    padding: 5px 13px;
    color: white;
    background-color: #33d951;
    text-align: center;
    cursor: pointer;
    width: auto;
    float: left;
    font-size: 24px;
    border-radius: 50%;
    margin-left: 30px;
  }

  .details-container .whatsapp a {
    color: #fff !important;
  }

  .btn-primary.focus,
  .btn-primary:focus {
    box-shadow: none !important;
  }
  .btn {
    border-radius: 3px !important;
  }

  .success-heading {
    text-align: center;
    margin: 50px 0 20px 0;
  }

  .success-msg {
    text-align: center;
  }

  .payment-icon {
    text-align: center;
    float: left;
    width: 100%;
  }

  .evi-text {
    vertical-align: middle;
    text-align: center;
    width: 100%;
    float: left;
    margin-bottom: 30px;
  }

  .details-container .twitter {
    border: none;
    outline: 0;
    padding: 5px 15px;
    color: white;
    background-color: #20bced;
    text-align: center;
    cursor: pointer;
    width: auto;
    float: left;
    font-size: 24px;
    border-radius: 3px;
    margin: 5px 10px 0 0;
  }
  .details-container .twitter a {
    color: #fff !important;
  }
  .details-container .twitter:hover {
    opacity: 0.7;
    border-radius: 3px;
    color: #fff !important;
  }

  .details-container .facebook {
    border: none;
    outline: 0;
    padding: 5px 15px;
    color: white;
    background-color: #5172d0;
    text-align: center;
    cursor: pointer;
    width: auto;
    float: left;
    font-size: 24px;
    border-radius: 3px;
    margin: 5px 10px 0 0;
  }
  .details-container .facebook a {
    color: #fff !important;
  }

  .details-container .facebook:hover {
    opacity: 0.7;
    border-radius: 3px;
    color: #fff !important;
  }

  .clear {
    width: 100%;
    float: left;
  }

  .details-container ul {
    float: left;
    width: 100%;
    margin: 20px 0px 20px 20px;
    padding: 0px;
  }

  .details-container ul li {
    float: left;
    width: 48%;
    margin: 0 10px 5px 0;
    list-style-type: square;
    padding: 0px;
    color: #333;
  }

  .upsells-container {
    float: left;
    width: 100%;
  }

  .star-rating {
    color: #ccc;
    position: relative;
    top: 7px;
    float: left;
  }

  .star-rating label {
    color: #bbb;
    font-size: 18px;
    padding: 0;
    cursor: pointer;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    float: right;
  }
  .star-rating input[type="radio"] {
    display: none;
  }

  .star-rating-title {
    float: left;
    margin-right: 10px;
  }

  .star-rating label:hover,
  .star-rating label:hover ~ label,
  .star-rating input[type="radio"]:checked ~ label {
    color: orange;
  }

  .star-rating .fa {
    margin-right: 5px;
    font-size: 20px;
  }

  .staricon,
  .staricon-before:before {
    font-family: dashicons;
    display: inline-block;
    line-height: 1;
    font-weight: 400;
    font-style: normal;
    speak: never;
    text-decoration: inherit;
    text-transform: none;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 20px;
    height: 20px;
    font-size: 20px;
    vertical-align: top;
    text-align: center;
    transition: color 0.1s ease-in;
  }
  .staricon-star-filled:before {
    content: "\f155";
  }
  .staricon-star-half:before {
    content: "\f459";
  }
  .staricon-star-empty:before {
    content: "\f154";
  }

  .price-tag {
    display: flex;
    align-items: center;
    width: 100%;
    .price {
      border-right: 1px solid #cfd7e5;
      display: inline-flex;
      line-height: 42px;
      padding-right: 20px;
      margin-right: 10px;
      @media (max-width: 992px) {
        font-size: 30px;
        line-height: 40px;
      }
    }
    .price-tag-image {
      max-width: 140px;
    }
  }

  .bac_credometic_span {
    background-color: #dfe6f1;
    border-radius: 40px;
    color: #3c525d;
    font-size: 14px;
    font-weight: 500;
    padding: 5px 20px;
  }
  .bottom-actions {
    display: inline-flex;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin: 40px 0;
    @media (max-width: 992px) {
      gap: 10px;
      margin: 20px 0;
    }
  }
  .buy-now {
    display: block;
    text-decoration: none;
    background-color: #0075b9;
    border: 1px solid #cfd7e5;
    color: white;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 500;
    line-height: 48px;
    text-align: center;
    img {
      margin-left: 10px;
    }
  }
  .details-container-info {
    margin-top: 50px;
    @media (max-width: 767px) {
      margin-top: 15px;
    }
    .descripcions {
      h3 {
        font-size: 25px;
        line-height: 27px;
        color: #3c525d;
        font-weight: 600;
        padding-bottom: 15px;
        border-bottom: 1px solid #cfd7e5;
        margin-bottom: 20px;
        @media (max-width: 767px) {
          font-size: 18px;
          line-height: 24px;
        }
      }
      ul {
        margin: 0;
      }
      p {
        color: #3c525d;
        font-size: 15px;
        line-height: 30px;
        font-weight: 400;
      }
      li {
        list-style: none;
        background: url(../images/new-images/icon/li-before.svg) no-repeat;
        background-position: 0 1px;
        background-size: 20px;
        padding-left: 35px;
        margin: 0 0 25px;
        @media (max-width: 767px) {
          margin: 0 0 15px;
        }
        p {
          margin: 0;
        }
      }
    }
    .review-tittle {
      padding-bottom: 15px;
      border-bottom: 1px solid #cfd7e5;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      @media (max-width: 1024px) {
        flex-wrap: wrap;
      }
      @media (max-width: 767px) {
        flex-direction: column;
        margin: 0 0 30px;
      }
      h3 {
        border: none;
        padding: 0;
        margin: 0;
        @media (max-width: 767px) {
          width: 100%;
        }
      }
      .review-count {
        color: #3c525d;
        font-size: 14px;
        line-height: 27px;
        margin-left: 20px;
        padding-left: 20px;
        border-left: 1px solid #cfd7e5;
        @media (max-width: 767px) {
          width: 100%;
          border-left: none;
          border-top: 1px solid #cfd7e5;
          margin: 10px 0 0;
          padding: 10px 0 0;
        }
        img {
          margin-right: 10px;
        }
      }
    }
    .review-box {
      position: relative;

      &__tittlename {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        @media (max-width: 1024px) {
          flex-wrap: wrap;
        }
        @media (max-width: 767px) {
          flex-direction: column;
        }
      }

      &__imagearea {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-right: 1px solid #cfd7e5;
        padding-right: 30px;
        margin-right: 18px;
        @media (max-width: 767px) {
          width: 100%;
          padding: 0;
          margin: 0 0 15px;
          border-right: 0;
        }
        span {
          width: 54px;
          height: 54px;
          border-radius: 50%;
          margin-right: 17px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .namearea {
          h4 {
            color: #3c525d;
            font-size: 15px;
            line-height: 27px;
            margin: 0;
            font-weight: 600;
            @media (max-width: 767px) {
              margin: 0 0 2px;
            }
          }
          span {
            color: #3c525d;
            font-size: 12px;
            line-height: 27px;
            margin: 0;
            font-weight: 400;
            &.review-date{
              position: relative;
              @media (max-width: 767px) {
                padding-left: 10px;
                margin-left: 15px;
                border-left: 1px solid #CFD7E5;
                width: auto;
                height: auto;
                border-radius: 0;
                line-height: 12px; 
              }
            }
          }
        }
      }

      &__rating {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 14px;
        line-height: 27px;
        color: #3c525d;
        @media (max-width: 767px) {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 2px;
          font-size: 11px;
          font-weight: 500;
          line-height: 21px;
        }
        .stararea {
          margin-right: 20px;
          .staricon {
            display: inline-block;
            font: normal normal normal 14px / 1 FontAwesome;
            font-size: inherit;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            color: #f8ba22;
            font-size: 20px;
            margin: 0 5px 0 0;
            
          }
          .staricon-star-filled {
            &:before {
              content: "\f005";
            }
          }
          .staricon-star-half {
            &:before {
              content: "\f123";
            }
          }
          .staricon-star-empty {
            &:before {
              content: "\f006";
            }
          }
        }
      }

      &__contentarea {
        margin: 30px 0 30px;
        @media (max-width: 767px) {
          margin: 15px 0 40px;
        }
        h4 {
          color: #3c525d;
          font-size: 20px;
          line-height: 27px;
          font-weight: 600;
          margin: 0 0 12px;
        }
        p {
          color: #3c525d;
          font-size: 15px;
          line-height: 30px;
          margin: 0;
        }
      }
    }
    .review-under-button {
      display: flex;
      align-items: center;
      gap: 20px;
      @media (max-width: 1024px) {
        flex-wrap: wrap;
      }
      .fillbtn {
        position: relative;
        border-color: #0075b9 !important;
        background-color: #0075b9;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        padding: 12px 20px;
        transition: 0.3s all ease-in-out;
        text-decoration: none;
        @media (max-width: 767px) {
          width: 100% !important;
          padding: 14px 10px;
          text-align: center;
          font-size: 14px;
        }
        svg {
          margin-left: 10px;
        }
        &:hover {
          background-color: #fff;
          color: #0075b9;
          svg {
            path {
              stroke: #0075b9;
            }
          }
        }
      }

      .outlinebtn {
        position: relative;
        border: 1px solid #0075b9;
        background-color: #fff;
        color: #0075b9;
        font-size: 16px;
        font-weight: 600;
        padding: 12px 20px;
        transition: 0.3s all ease-in-out;
        text-decoration: none;
        border-radius: 50px;
        @media (max-width: 767px) {
          width: 100%;
          padding: 10px;
          text-align: center;
          font-size: 14px;
        }
        svg {
          margin-left: 10px;
        }
        &:hover {
          background-color: #0075b9;
          color: #fff;
          svg {
            path {
              stroke: #fff;
            }
          }
        }
      }
    }
  }
  .bac_cred_section {
    display: block;
    width: 100%;
    margin-top: 8px;
    @media (max-width: 767px) {
      margin-top: 15px;
    }
  }
  // PRODUCT RIGHT TEXT EDS

  .review-submit-section {
    position: relative;
    width: 100%;
    max-width: 600px;
    display: inline-block;
    margin-top: 30px;
    border: 1px solid #dfe6f1;
    padding: 30px;
    border-radius: 16px;

    .full-width {
      width: 100%;
      display: inline-block;
    }

    .star-rating-title {
      font-size: 25px;
      line-height: 27px;
      color: #3c525d;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .star-rating {
      display: block;
      label {
        color: #dfe6f1;
      }
    }

    .form-group {
      margin-bottom: 35px;
      label {
        font-weight: 500;
        font-size: 15px;
        color: #3c525d;
      }
      .form-control {
        border: none;
        border-bottom: 1px solid #cfd7e5;
        color: #3c525d;
        font-size: 13px;
        line-height: 27px;
        margin-bottom: 0;
        padding: 10px 10px 10px;
        border-radius: 0px;
        background-color: transparent;
        max-height: 50px;
        outline: none;
      }
    }
  }
}
/*--------- Catagory section page ---------*/
.catagory-left-panel {
  border-right: 1px solid #cfd7e5;
  padding-right: 15px !important;
  padding-top: 15px;
  .filter-title {
    font-size: 25px;
    line-height: 27px;
    color: #3c525d;
    font-weight: 600;
    display: inline-block;
  }
  .navigation {
    background-color: transparent;
    position: relative;
    border-bottom: 1px solid #cfd7e5;
    border-radius: 0;
    h2 {
      font-size: 16px;
      line-height: 27px;
      color: #3c525d;
      margin-bottom: 10px;
      font-weight: 600;
      display: block;
      position: relative;
      @include responsive(desktop) {
        font-size: 24px;
      }
      .filter-menu-title {
        background-color: transparent;
        width: 100%;
        text-align: left;
        padding: 0 20px 0 0;
        img {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
  .mainmenu {
    .dropdown-list-menu {
      position: relative;
    }
    a {
      padding: 5px 0;
      color: #3c525d;
      font-size: 14px;
      line-height: 47px;
      font-weight: 400;
      &:before {
        display: none;
      }
    }
    .submenu {
      overflow: hidden;
      max-height: 0;
      -webkit-transition: all 0.5s ease-out;
      position: relative;
      z-index: 999;
      li {
        a {
          position: relative;
          color: #0075b9 !important;
          padding: 0 0 0 30px;
          &:before {
            position: absolute;
            content: "";
            display: block !important;
            opacity: 1;
            width: 20px;
            height: 10px;
            background: url(../images/new-images/icon/submenu-arrow.svg) no-repeat center;
            background-size: 100%;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
  }
}

.category-similer-box-anchore {
  &:hover {
    text-decoration: none;
  }
}

.catagory-container-right {
  padding-left: 30px !important;
  padding-top: 20px;
  @media only screen and (max-width: 767px) {
    padding-left: 15px !important;
  }
  .top-listing {
    font-size: 35px;
    color: #3c525d;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .similar-catagory {
    position: relative;
    width: 100%;
    height: 180px;
    border-radius: 30px;
    overflow: hidden;
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    padding: 10px 15px;
    img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      left: 0;
      top: 0;
    }
    h4 {
      position: relative;
      font-size: 22px;
      line-height: 30px;
      color: #fff;
      margin-bottom: 10px;
      width: 100%;
      text-transform: capitalize;
    }
    .category-similer-box-link {
      position: relative;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      text-decoration: none !important;
      img {
        position: relative;
        width: auto;
        height: auto;
        margin-left: 7px;
      }
    }
  }

  .deal-link-item {
    border-color: #c0cee4 !important;
    border-radius: 30px !important;
    padding: 10px 10px 20px;
    h2 {
      a {
        color: #3c525d;
      }
    }
  }

  .sticky-badge {
    position: absolute;
      top: -10px; /* Adjust to ensure visibility */
      left: 0px; /* Adjust to ensure visibility */
      background: linear-gradient(45deg, #888888, #666666); /* Gradient background */
        color: white;
        padding: 4px 30px;
        font-size: 16px;
        font-weight: bold;
        z-index: 1000;
        box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.5); /* Stronger shadow for a 3D effect */
        //border-radius: 8px; /* Rounded corners */
        text-align: center;
  }

  .sticky-badge::after {
      content: '';
      position: absolute;
      left: 100%; /* Position at the right edge */
      top: 50%;
      transform: translateY(-50%);
      border-width: 16px; /* Control arrow size */
      border-style: solid;
      border-color: transparent transparent transparent #666666;
  }
  .pagination {
    align-items: center;
    justify-content: center;
    li {
      margin: 0 3px;
      a {
        border: none;
        color: #3c525d;
        font-size: 14px;
      }
      &.active {
        a {
          border-radius: 50%;
          color: #fff;
          padding: 2px;
          background-color: #0075b9;
          width: 25px;
          height: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.filter-cat-item {
  padding: 0px 10px !important;
}

.listing-container {
  background: #fff;
}

.sub-banner-container-desktop {
  width: 100%;
  min-height: 150px;
  text-align: center;
  margin-bottom: 30px;
  background-size: cover !important;
}

.sub-banner-container-mobile {
  width: 100%;
  min-height: 150px;
  text-align: center;
  margin-bottom: 30px;
  background-size: cover !important;
  display: none;
}

.outofstock-page-details {
  padding: 5px 10px;
  border: 1px solid #f5cccc;
  color: #de1d1d;
  background-color: #f5cccc;
  border-radius: 15px;
}

.outofstock {
  position: absolute;
  top: 30px;
  right: 0;
  width: auto;
  font-size: 12px;
  font-weight: 500;
  font-family: "Poppins", sans-serif;
  // float: left;
  display: flex;
  align-items: center;
  justify-content: center;
  // margin-top: 20px;
  padding: 5px 10px;
  border: 1px solid #f5cccc;
  color: #de1d1d;
  background-color: #f5cccc;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  svg {
    margin-right: 4px;
  }
}
.tooltip-btn {
  position: relative;
}
.tooltip-text {
  visibility: hidden;
  position: absolute;
  width: 160px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  padding: 10px;
  left: -68px;
  top: 35px;
  color: #fff;
  z-index: 9999;
  font-size: 10px;
  &:before {
    position: absolute;
    content: "";
    left: 0;
    top: -9px;
    right: 0;
    margin: auto;
    width: 0px;
    height: 0px;
    border-style: solid;
    border-width: 0 7.5px 9px 7.5px;
    border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
    transform: rotate(0deg);
  }
}
.tooltip-btn:hover .tooltip-text {
  visibility: visible;
}
.banner-container .product-title {
  float: none;
  position: absolute;
  width: 100%;
  text-align: center;
  font-size: 48px;
  font-weight: 700;
  padding: 63px 0;
  text-transform: uppercase;
  color: #fff;
}

.filter {
  width: 100%;
  text-align: left;
  font-size: 16px;
}
.fields {
  width: 100%;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #cfd7e5;
  padding: 15px 0px 15px 0;
  color: #3c525d;
  text-align: right;
}
.fields ul {
  margin: 0;
  padding: 0;
}
.fields ul li {
  color: #3c525d;
  font-size: 14px;
  line-height: 47px;
  display: flex;
  input {
    margin: 0 7px 0 0;
  }
}
.fields1 {
  width: 100%;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #cfd7e5;
  padding: 15px 0px 15px 0;
  color: #3c525d;
}
.lebel {
  font-size: 24px;
  line-height: 27px;
  color: #3c525d;
  margin-bottom: 0px;
  font-weight: 600;
  text-align: left;
}

.form-groups {
  position: relative;
  margin-bottom: 0px;
  span {
    position: absolute;
    left: 20px;
    top: 61%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #3c525d;
  }
  input {
    padding: 0 15px 0 32px !important;
    // background: #f4f7fa url(../images/new-images/icon/filter-arrow.svg) no-repeat !important;
    // background-position: 94% center !important;
    // background-size: 14px !important;
  }
}
.filter input[type="text"] {
  background-color: #f4f7fa;
  border: none;
  height: 50px;
  font-size: 14px;
  color: #3c525d;
  width: 100%;
  border-radius: 50px;
  padding: 0 20px;
  margin-top: 15px;
}

.filter input[type="text"]:focus {
  outline: none;
}

.filter select {
  background: #f4f7fa url(../images/new-images/icon/filter-arrow.svg) no-repeat !important;
  background-position: 94% center !important;
  background-size: 14px !important;
  border: none;
  margin-bottom: 0;
  height: 50px;
  margin-right: 0;
  font-size: 14px;
  line-height: 14px;
  border-radius: 50px;
  width: 100%;
  padding: 0 20px;
  -moz-appearance: none; /* Firefox */
  -webkit-appearance: none; /* Safari and Chrome */
  appearance: none;
}
.filter select:focus {
  border: 1px solid #ccc;
  outline: none;
}

input:focus,
textarea:focus,
select:focus {
  background: white;
  border: 1px solid #ccc;
  outline: none;
}

.filter-icon {
  float: right;
  width: 38px;
  height: 38px;
  border-radius: 3px;
  border: 1px solid #0075B9;
  overflow: hidden;
  margin-right: 0px;
  margin-top: 12px;
}
.form-control:focus{
  box-shadow: none !important;
}

.filter input[type="submit"] {
  background-color: #0075B9;
  border: none;
  text-decoration: none;
  color: white;
  padding: 8px 15px;
  margin-right: 5px;
  cursor: pointer;
  border-radius: 30px;
  margin-bottom: 0px;
  font-size: 12px;
  margin-top: 10px;
  font-weight: 500;
}
.filter input[type="submit"]:hover {
  background-color: #1899c1;
}
/*     
#menu { overflow: auto; position:relative; z-index:2; } .parent-menu { background-color: #0c8fff; min-width:200px; float:left; } #menu ul { list-style-type:none; } #menu ul li a { padding:10px 15px; display:block; color:#fff; text-decoration:none; } #menu ul li a:hover { background-color:#007ee9; }

#menu ul li:hover > ul { left: 200px; -webkit-transition: left 200ms ease-in; -moz-transition: left 200ms ease-in; -ms-transition: left 200ms ease-in; transition: left 200ms ease-in; } #menu ul li > ul { position: absolute; background-color: #333; top: 0; left: -200px; min-width: 200px; z-index: -1; height: 100%; -webkit-transition: left 200ms ease-in; -moz-transition: left 200ms ease-in; -ms-transition: left 200ms ease-in; transition: left 200ms ease-in; } #menu ul li > ul li a:hover { background-color:#2e2e2e; } */

/* reset our lists to remove bullet points and padding */

.right {
  float: right;
  margin: 8px 0 0 0;
  font-weight: 300;
  font-size: 13px;
}

.left {
  transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
}

.up {
  transform: rotate(-135deg);
  -webkit-transform: rotate(-135deg);
}

.down {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
}

.listing-container {
  width: 100%;
}

.listing-container .product-title h2 span {
  color: #777777;
  font-size: 18px;
}

.listing-container .deal-link-item {
  position: relative;
  padding: 4px 10px 10px;
  text-decoration: none;
  color: #333;
  text-align: center;
  float: left;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-bottom: 20px;
}

.listing-container .deal-link-item figure {
  text-align: center;
  float: left;
  width: 100%;
  margin-bottom: 10px;
  min-height: 223px;
}
.listing-container .deal-link-item .heading-wrap {
  text-align: center;
  float: left;
  width: 100%;
}
.listing-container .deal-link-item .heading-wrap h2 {
  // font-size: 14px;
  // color: #333;
  // line-height: 24px;
  // font-weight: 700;
  // margin-bottom: 0px;
  // text-align: center;
  // float: left;
  // width: 100%;
}
.listing-container .deal-link-item .heading-wrap h2 a {
  // color: #333 !important;
  // text-decoration: none;
}

.listing-container .deal-link-item .heading-wrap h2 a:hover {
  // color: #000 !important;
  // text-decoration: none;
}

.listing-container .deal-link-item .heading-wrap h3 {
  // font-size: 14px;
  // line-height: 24px;
  // color: #333;
  // text-align: center;
  // float: left;
  // width: 100%;
}

.listing-container .deal-link-item .heading-wrap .price {
  // font-size: 20px;
  // color: #ff0000;
  // line-height: 24px;
  // font-weight: 700;
  // margin-bottom: 8px;
  // text-align: center;
  // float: left;
  // width: 100%;
}
.listing-container .oldprice {
  text-decoration: line-through;
}

.listing-container .deal-link-item button {
  border: none;
  outline: 0;
  padding: 0;
  background-color: transparent;
  text-align: center;
  cursor: pointer;
}

.listing-container .deal-link-item button:hover {
  // opacity: 0.8;
  // border-radius: 3px;
  // background: #ff0000;
  // color: #fff;
}

/* .sidenav .closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px;
  margin-left: 50px;
} */

.sidenav .closebtn {
  position: absolute;
  top: 0;
  right: 5px;
  font-size: 36px;
  padding: 0 5px 0 13px;
  z-index: 9999;
  background: #0075B9;
}

._2iA8p44d0WZ-WqRBGcAuEV {
  border: none !important;
  border-radius: 0px !important;
  padding: 0 !important;
  min-height: auto !important;
  margin-bottom: 0px;
  input {
    background: #f4f7fa url(../images/new-images/icon/filter-arrow.svg) no-repeat !important;
    background-position: 94% center !important;
    background-size: 14px !important;
  }
}

.cmd-border-bottom {
  .container {
    border-bottom: 1px solid #cfd7e5;
  }
}
.optionContainer {
  li.option {
    display: flex;
    font-size: 16px;
    line-height: 24px;
    &.lhyQmCtWOINviMz0WG_gr {
      background: #cfd7e5;
      color: #3c525d;
    }
    &:hover {
      background: #cfd7e5;
      color: #3c525d;
    }
    input {
      margin-right: 5px;
    }
  }
}
.list-more {
  color: #1675b9;
  font-size: 16px;
  text-transform: capitalize;
  margin-left: 19px;
  margin-top: 8px;
  display: inline-block;
}
._7ahQImyV4dj0EpcNOjnwA {
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 300 !important;
}

.sidebar-toggle-title-wrapper {
  position: relative;
}

.sidebar-toggle-title {
  background-color: transparent;
  width: 100%;
  text-align: left;
  padding: 0 20px 0 0;
  font-size: 16px;
  line-height: normal;
  @include responsive(desktop) {
    font-size: 24px;
  }
  img {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.select-dropdown {
  margin-top: 15px;
}

.category-similer-box-wrapper {
  padding: 0px 8px !important;
}

.mobile {
  .navigation {
    flex-direction: column;
  }
  .main-menu {
    z-index: 9;
  }
  .form-container {
    max-width: 100%;
  }
}

.product-details-header{
  position: relative;
  padding: 12px 0;
  display: none;
  @media (max-width: 767px) {
    display: block;
  }
  &__svg{
    width: 35px;
    height: 35px;
    border-radius: 6px;
    background-color: #ECF2F8;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__title{
    font-size: 16px;
    line-height: 30px;
    font-weight: 500;
    color: #131921;
    margin: 0 0 0 20px;
  }
}
