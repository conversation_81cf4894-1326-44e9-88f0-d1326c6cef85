/* Footer css */

.main-footer {
  background: #f2f6f8;
  padding: 140px 0 20px 0;
  color: #3b3a3a;
  position: relative;
  float: left;
  width: 100%;
  margin-top: 30px;
  @media (max-width: 767px) {
    padding: 0 0 20px;
  }
}

.footer-block {
  width: 100%;
  position: relative;
  float: left;
  margin-bottom: 30px;
}

.footer-block h3 {
  width: 100%;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #3c525d;
}
.footer-block ul li {
  list-style-type: none;
  display: block;
  font-size: 14px;
  line-height: 34px;
}
.footer-block ul li:last-child {
  margin-bottom: 0;
}
.footer-block ul li a {
  font-size: 14px;
  line-height: 34px;
  color: #3c525d;
  text-decoration: none;
}
.footer-block p {
  font-size: 14px;
  color: #3c525d;
  line-height: 25px;
  .main-address {
    font-size: 16px;
    font-weight: 600;
  }
}
.card-wrap {
  /*    position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%,-50%); */
  float: left;
  margin-top: 20px;
  width: 100%;
}
.card-wrap a:first-child {
  margin-right: 10px;
}
.footer-block form {
  margin-top: 36px;
}
.footer-block form input {
  height: 40px;
  border-radius: 3px;
  background: #fff;
  padding: 0 18px;
  font-size: 14px;
  line-height: 14px;
  color: #3b3a3a;
  font-family: "Poppins Regular";
  border: none;
  width: 100%;
}
.footer-block form input:focus {
  outline: none;
}
.footer-block form button {
  display: block;
  margin-top: 12px;
  height: 40px;
  border-radius: 3px;
  color: #fff;
  background: #ff0000;
  width: 110px;
  text-align: center;
}
.footer-block form button:hover {
  background: #000;
  color: #fff;
}
.footer-block form button:focus {
  outline: none;
}
.copyright {
  padding: 20px 0;
  width: 100%;
  border-top: 1px solid #dce4e8;
  display: inline-block;
  text-align: center;
  @media (max-width: 767px) {
    padding: 30px 0;
    display: none;
  }
  @media only screen and (min-width: 1024px) {
    text-align: left;
  }
  .card-wrap {
    margin: 15px 0;
    float: none;
    @media only screen and (min-width: 1024px) {
      margin: 0;
    }
    img {
      margin: 4px;
      border: 1px solid #e7e7e7;
    }
  }
}
.copyright p {
  color: #3c525d;
  line-height: 14px;
  font-size: 14px;
  margin: 0;
}
.bottom-footer-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  @media only screen and (min-width: 767px) {
    justify-content: flex-start;
  }
  li {
    margin-right: 15px;
    &:last-child {
      margin-right: 0;
    }
    a {
      font-size: 14px;
      color: #3c525d;
    }
  }
}
// .mobile {

// }
// .desktop {
//   display: block;
// }

.mobile {
  display: none;
  @media (max-width: 767px) {
    display: block;
  }
}
.desktop {
  display: block;
  @media (max-width: 767px) {
    display: none;
  }
}

#address {
  padding-left: 32px;
}
.address-bar {
  background: url(../images/new-images/icon/footer-address.svg) no-repeat;
  background-size: 20px;
  background-position: 0 3px;
}
.contact-bar {
  background: url(../images/new-images/icon/footer-call.svg) no-repeat;
  background-size: 20px;
  background-position: 0 3px;
}
.footer-social-ico {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
.footerApiLogos {
  margin-top: 20px;
  display: flex;
  @media (max-width: 1140px) {
    flex-wrap: wrap;
    gap: 5px;
  }
  @media (max-width: 767px) {
    flex-wrap: nowrap;
  }
  @media only screen and (min-width: 1024px) {
    margin-top: 42px;
    display: block;
  }
  a {
    display: block;
    margin: 0 3px;
    @media only screen and (min-width: 1024px) {
      margin: 8px 0 0;
    }
  }
}
#newsletterft {
  background-color: #fff;
  padding: 20px;
  border-radius: 16px;
  @media only screen and (min-width: 1024px) {
    margin-top: -50px;
    padding: 50px;
  }
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #3c525d;
  }

  p {
    font-size: 14px;
    color: #3c525d;
    margin-bottom: 18px;
  }
  form {
    margin-top: 0;
    button {
      background: #0075b9;
      color: #fff;
      font-size: 14px;
      border-radius: 44px !important;
      max-width: 150px;
      width: 100%;
      margin-top: 15px;
      img {
        margin-left: 10px;
      }
    }
  }
  .input-text {
    font-size: 14px;
    color: #0075b9;
    padding: 10px 19px 9px;
    border-radius: 50px;
    border: 1px solid #0075b933;
    &::placeholder {
      color: #0075b9;
      opacity: 1;
    }

    &::-ms-input-placeholder {
      color: #0075b9;
    }
  }
}

/* contact info Section */
.contact-info-container {
  background: #fff;
  padding: 20px 0 0;
  position: relative;
  z-index: 1;
  @media only screen and (min-width: 1024px) {
    padding: 0;
  }
}
.contact-info-wrap {
  max-width: 900px;
  width: 100%;
  margin: auto;
  border-radius: 12px;
  overflow: hidden;
  @media only screen and (min-width: 992px) {
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: -30px;
  }
  .slick-slide {
    padding: 0 6px;
    box-sizing: border-box;
  }
  .color1 {
    background-color: #0075b9;
  }
  .color2 {
    background-color: #005d93;
  }
  .mailinfo {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 30px;
    @media only screen and (min-width: 1024px) {
      padding: 35px 10px;
    }
    &:hover {
      text-decoration: none;
    }
  }
  .info-text {
    margin-left: 15px;
    h3 {
      color: #fff;
      font-size: 20px;
      line-height: 27px;
      font-weight: 600;
      margin: 0;
      text-decoration: none;
    }
    p {
      font-size: 14px;
      line-height: 21px;
      margin: 0;
      color: #fff;
      text-decoration: none;
    }
  }
  .callinfo {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 30px;
    @media only screen and (min-width: 1024px) {
      padding: 35px 10px;
    }
  }
}

.bottom-sticky-footer-top {
  position: relative;
  margin-top: 60px;
}
.bottom-sticky-footer {
  position: fixed;
  width: 100%;
  height: auto;
  padding: 9px 20px 10px;
  background-color: #fff;
  box-shadow: 0 0 15px 0 rgba(125, 137, 157, 0.2);
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  ul {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
    a {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &.active {
        background-color: rgba(222, 29, 29, 1);
        path {
          fill: #fff;
        }
      }
    }
  }
}
.mobile-info {
  border-radius: 12px;
  .callinfo {
    img {
      max-height: auto;
      max-width: 33px;
    }
  }
  .mailinfo {
    img {
      max-height: auto;
      max-width: 33px;
    }
  }
  .info-text {
    text-align: left;
  }
}
.section-top-tag {
  position: relative;
  background-color: #fff;
  padding: 14px 0;
  border-bottom: 5px solid rgba(193, 205, 217, 1);
  ul {
    margin: 0;
    li {
      a {
        display: flex;
        align-items: center;
        gap: 2px;
        padding: 7px 10px;
        border-radius: 6px;
        background: rgba(236, 242, 248, 1);
        color: rgba(19, 25, 33, 1);
        font-weight: 500;
        &:hover {
          background-color: #aee1ff;
        }
      }
    }
  }
  .slick-slide {
    padding: 0 2px;
    display: flex;
    align-items: center;
  }
}
