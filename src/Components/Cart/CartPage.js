import React, {Component} from 'react';
import BannerSection from './BannerSection';
import BreadcrumbSection from './BreadcrumbSection';
import CartListingSection from './CartListingSection';
import MobileCartListingSection from './MobileCartListingSection';

import {isMobile} from 'react-device-detect';

export default class CartPage extends Component {
    componentDidMount(){
        window.Intercom('update', {
            "hide_default_launcher": true
        });
    }
    
    // render(){
    //     return <div className="">
    //                 <BannerSection />
    //                 <BreadcrumbSection />
    //                 <CartListingSection />
    //             </div>
    // }
    renderContent(){
        if (isMobile) {
            return <div className="">
            {/* <BannerSection />
            <BreadcrumbSection /> */}
            <MobileCartListingSection />
        </div>
        }
        return <div className="">
        <BannerSection />
        <BreadcrumbSection />
        <CartListingSection />
    </div>
}
render() {
    return this.renderContent();
}
}