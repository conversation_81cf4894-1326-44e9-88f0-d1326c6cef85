import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { connect } from "react-redux";
import { addToCart, addToCart2 } from "../../Services/Actions/cart.actions";

const MobileProductListItemComponent = ({
  product,
  addToCart,
  isCartAddError,
  cartErrorMessage,
}) => {
  const [clickedProductId, setClickedProductId] = useState("");
  const [addToCartButtonDisabled, setAddToCartButtonDisabled] = useState(false);
  const [addToCartButtonnLoader, setAddToCartButtonnLoader] =
    useState("hidden");
  const [addToCartButtonnCart, setAddToCartButtonnCart] = useState("");
  const [addToCartButton, setAddToCartButton] = useState(true);
  const [showCartReturnModal, setShowCartReturnModal] = useState(false);
  const [showUpdateCartMsgModal, setShowUpdateCartMsgModal] = useState(false);
  const [localCartErrorMessage, setLocalCartErrorMessage] = useState("");

  const [modelLink, setModelLink] = useState("");

  const toCapitalize = (str) => {
    return str
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const truncateText = (name, maxLength) => {
    return name.length > maxLength
      ? name.substring(0, maxLength) + "..."
      : name;
  };

  const openCartReturnPop = () => {
    setShowCartReturnModal(true);
  };

  const closeCartReturnPop = () => {
    setShowCartReturnModal(false);
  };

  const cartUpdatePopupResOpen = () => {
    setShowUpdateCartMsgModal(true);
  };

  const cartUpdatePopupResClose = () => {
    setShowUpdateCartMsgModal(false);
  };

  const handleOnAddToCart = (
    product_id,
    product_name,
    product_image,
    price,
    quantity,
    brand,
    modelo,
    category_name,
    is_allowed_bac_credomatic
  ) => {
    setAddToCartButtonnLoader("");
    setAddToCartButtonnCart("hidden");
    setAddToCartButton(true);
    setAddToCartButtonDisabled(true);
    setLocalCartErrorMessage("");
    setClickedProductId(product_id);

    let checked_variation_product_id = 0;
    addToCart(
      product_id,
      product_name,
      product_image,
      price,
      quantity,
      checked_variation_product_id,
      modelo,
      is_allowed_bac_credomatic
    );

    setTimeout(() => {
      if (isCartAddError) {
        setLocalCartErrorMessage(cartErrorMessage);
        setAddToCartButtonnLoader("hidden");
        setAddToCartButtonnCart("");
        setAddToCartButton(true);
        setAddToCartButtonDisabled(false);
        setClickedProductId("");

        cartUpdatePopupResOpen();
        setTimeout(() => cartUpdatePopupResClose(), 5000);
      } else {
        openCartReturnPop();
        setAddToCartButtonnLoader("hidden");
        setAddToCartButtonnCart("");
        setAddToCartButton(false);
        setAddToCartButtonDisabled(false);
        setLocalCartErrorMessage("");

        // Tracking events
        window.fbq("track", "AddToCart", {
          content_ids: [product_id],
          content_type: "product",
          value: price,
          currency: "USD",
        });

        window.ga("ec:addProduct", {
          id: modelo,
          name: brand + " " + modelo,
          price: price,
          brand: brand,
          category: category_name,
          quantity: 1,
        });
        window.ga("ec:setAction", "add");
        window.ga("send", "event", "Product List Page", "click", "addToCart");

        window.gtag("event", "add_to_cart", {
          send_to: "UA-521381-2/yuuFCLjX-aUBENbiovUC",
          value: price,
          currency: "USD",
          event_callback: true,
        });
      }
    }, 3000);
  };

  useEffect(() => {
    if (!!product && !!product?.modelo && !!product?.modeloch) {
      const model = product?.modelo;
      const checkSlash = "/";
      if (model.indexOf(checkSlash) !== -1) {
        setModelLink(product?.modeloch);
      } else {
        setModelLink(product?.modelo);
      }
    }
  }, [product]);

  return (
    <div className="item">
      <div className="item__pic">
        <Link title={product?.product_name} to={`/producto/${modelLink}`}>
          <img src={product?.product_image} alt="" />
        </Link>
      </div>
      <div className="item__info">
        {(product?.product_name && (
          <p className="item__name">
            <Link title={product?.product_name} to={`/producto/${modelLink}`}>
              {truncateText(toCapitalize(product?.product_name), 40)}
            </Link>
          </p>
        )) ||
          null}
        <div className="item__review">
          <img
            src={`${process.env.PUBLIC_URL}/images/new-images/icon/star.svg`}
            title="star"
            alt="star"
          />
          <span>
            {product.product_rating} | {product.review_count} reviews
          </span>
        </div>
        <div className="d-flex align-items-end mt-1">
          <p className="item__price">
            {product.special_price != null &&
            product.special_price != "" &&
            product.special_price != "0.00" ? (
              <span className="old">${product.regular_price}</span>
            ) : null}
            ${product.price}
          </p>
          <div className="item__action">
            {(() => {
              if (product.allow_purchase === 1 && product.addtocart_option) {
                if (product.is_avl_attributes) {
                  let modelNumber = product.modelo;
                  let checkSlash = "/";
                  let setModelNumber;
                  if (modelNumber.indexOf(checkSlash) !== -1) {
                    setModelNumber = product.modeloch;
                  } else {
                    setModelNumber = product.modelo;
                  }

                  return (
                    <div>
                      <p style={{ borderBottom: "0px" }}>
                        {product.avl_attributes.map((attribute, index) => (
                          <Link
                            key={index}
                            to="#"
                            style={{
                              backgroundColor: attribute.attribute_term_code,
                              padding: "0px 10px",
                              borderRadius: "20px",
                              margin: "0px 3px",
                            }}
                            title={attribute.attribute_term_name}
                            onClick={() =>
                              this.handleOnProductRedirect(setModelNumber)
                            }
                          ></Link>
                        ))}
                      </p>

                      <Link
                        to="#"
                        onClick={() =>
                          this.handleOnProductRedirect(setModelNumber)
                        }
                      >
                        Ver el producto
                      </Link>
                    </div>
                  );
                } else {
                  //if (this.state.addToCartButton) {
                  return (
                    <button
                      onClick={() =>
                        handleOnAddToCart(
                          product.product_id,
                          product.product_name,
                          product.product_image,
                          product.price,
                          "1",
                          product.brand,
                          product.modelo,
                          product.category_name,
                          product.is_allowed_bac_credomatic
                        )
                      }
                      disabled={addToCartButtonDisabled}
                    >
                      {(() => {
                        if (clickedProductId == product.product_id) {
                          return (
                            <>
                              <img
                                src={`${process.env.PUBLIC_URL}/images/new-images/icon/top-cart.svg`}
                                width="20"
                                height="16"
                                alt="Cart"
                              />
                            </>
                          );
                        } else {
                          return (
                            <>
                              <img
                                src={`${process.env.PUBLIC_URL}/images/new-images/icon/top-cart.svg`}
                                width="20"
                                height="16"
                                alt="Cart"
                              />
                            </>
                          );
                        }
                      })()}
                    </button>
                  );
                }
              } else {
                return (
                  <div>
                    <span className="outofstock">
                      <svg
                        width="18px"
                        height="18px"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#de1d1d"
                      >
                        <g strokeWidth="0" />
                        <g strokeLinecap="round" strokeLinejoin="round" />
                        <g>
                          <path
                            stroke="#de1d1d"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 9v4m0 3v.01M5.313 20h13.374c1.505 0 2.471-1.6 1.77-2.931L13.77 4.363c-.75-1.425-2.79-1.425-3.54 0L3.543 17.068C2.842 18.4 3.808 20 5.313 20Z"
                          />{" "}
                        </g>
                      </svg>
                      Fuera de Stock
                    </span>
                    <button
                      onClick={() => this.openNotifyPop(product.product_id)}
                      className="tooltip-btn"
                    >
                      <svg
                        width="24px"
                        height="24px"
                        viewBox="0 0 24 24"
                        fill="#de1d1d"
                      >
                        <g strokeWidth="0" />
                        <g strokeLinecap="round" strokeLinejoin="round" />
                        <g>
                          {" "}
                          <title />{" "}
                          <g>
                            {" "}
                            <g>
                              {" "}
                              <g>
                                {" "}
                                <path
                                  d="M18.9,11.2s0-8.7-6.9-8.7-6.9,8.7-6.9,8.7v3.9L2.5,17.5h19l-2.6-2.4Z"
                                  fill="none"
                                  stroke="#de1d1d"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                />{" "}
                                <path
                                  d="M14.5,20.5s-.5,1-2.5,1-2.5-1-2.5-1"
                                  fill="none"
                                  stroke="#de1d1d"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                />{" "}
                              </g>{" "}
                            </g>{" "}
                          </g>{" "}
                        </g>
                      </svg>
                      <span className="tooltip-text">
                        Recibir notificación sobre disponibilidad
                      </span>
                    </button>
                  </div>
                );
              }
            })()}
          </div>
        </div>
      </div>
    </div>
  );
};

function mapStateToProps(state) {
  return {
    isCartAddError: state.cart.isCartAddError,
    cartErrorMessage: state.cart.cartErrorMessage,
  };
}

const actionCreators = {
  addToCart: addToCart2,
};

export const MobileProductListItem = connect(
  mapStateToProps,
  actionCreators
)(MobileProductListItemComponent);
