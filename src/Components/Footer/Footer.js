import React, {Component} from 'react';
import QuickLinksSection from './QuickLinksSection';
import ContactDetailsSection from './ContactDetailsSection';
import SocialLinksSection from './SocialLinksSection';
import NewsletterSubmitSection from './NewsletterSubmitSection';
import MobileFooterSticky from './MobileFooterSticky';

import {isMobile} from 'react-device-detect';

export default class Footer extends Component {
    renderContent = () => {
        if (isMobile) {
            return <div className="bottom-sticky-footer-top">
                    <MobileFooterSticky />
                    </div>
        }
        return <footer className="main-footer">
                <div className="container">
                    <div className="row">
                        <QuickLinksSection />
                        <ContactDetailsSection />
                        <SocialLinksSection />
                        <NewsletterSubmitSection />
                    </div>
                </div>
            </footer>
        
    }
    render(){
        return this.renderContent();
    }
}