import React, { useState,useRef ,useEffect } from 'react';
import algoliasearch  from 'algoliasearch/lite';
import {
  InstantSearch,
  SearchBox,
  Configure,
  Hits,
  Highlight,
  useInfiniteHits,
  useSearchBox, 
} from 'react-instantsearch-hooks-web';

import { Link ,useHistory  } from "react-router-dom";

const searchClient = algoliasearch('81JEEVCUGY', '********************************');

function CustomInfiniteHits() {
  const { hits, isLastPage, showMore } = useInfiniteHits();
  const { query,refine } = useSearchBox();
  const history = useHistory();
    const handleOnProductRedirect = (
    productId,
    productName,
    productPrice,
    productImage,
    productRegularPrice,
    allowPurchase,
    productAvailableQuantity,
    isStored = false
  ) => {
    if (!isStored) {
      const prodObj = {
        product_id: productId,
        product_title: productName,
        product_price: productPrice,
        product_image: productImage,
        product_regular_price: productRegularPrice,
        allow_purchase: allowPurchase,
        product_available_quantity: productAvailableQuantity,
      };

      // const exists = clickedSearchData.some(
      //   (item) => item.product_id === productId
      // );

      // if (!exists) {
      //   const updatedData = [...clickedSearchData, prodObj];
      //   setClickedSearchData(updatedData);
      //   localStorage.setItem("searchData", JSON.stringify(updatedData));
      // }
    }
    refine('');
    history.push(`/producto/${productId}`);
  };

  
  return query.trim() === '' ? null : (
    <ul className="autocomplete">
      {hits.length === 0 ? (
        <li>No se han encontrado resultados!</li>
      ) : (
        <>
          {hits.map((hit) => (
            <li key={hit.objectID}>
              <Link to="#"  onClick={(e) =>
                      
                      handleOnProductRedirect(
                        hit.product_id,
                        hit.product_title,
                        hit.precio,
                        hit.product_image,
                        hit.special_price2,
                        hit.allow_purchase,
                        0
                      )
                    }>
              <div className="img-sec">
                <img className="prod-img" src={hit.product_image} alt={hit.name} />
              </div>
              <div className="desc-sec">
                <p>
                  <Highlight attribute="product_title" hit={hit} />
                </p>
                  {(() => {
                        if (hit.product_price === "0.00") {
                          return <></>;
                        } else {
                          return (
                            <p>
                              <span className="price-span">Precio:</span>
                              <span className="sell-price-span">
                                {" "}
                                 ${<Highlight hit={hit} attribute="product_price" />}
                              </span>

                              {hit.product_regular_price ? (
                                <span className="reg-price-span">
                                  ${<Highlight hit={hit} attribute="product_regular_price" />}
                                </span>
                              ) : (
                                <></>
                              )}
                            </p>
                          );
                        }
                      })()}
                        {(() => {
                        if (hit.allow_purchase === 1) {
                          if (hit.current_prod_qty > 0) {
                            return <p className="instock-sec">En Stock</p>;
                          } else {
                            return (
                              <p className="outstock-sec">Fuera de stock</p>
                            );
                          }
                        } else {
                          return <p className="outstock-sec">Fuera de stock</p>;
                        }
                      })()}
                
              </div>
            </Link>
            </li>
          ))}
          {!isLastPage && (
            <li className="load-more">
              <button onClick={showMore} className="load-more-btn">Mostrar más</button>
            </li>
          )}
        </>
      )}
    </ul>
  );
}

// Custom SearchBox using connectSearchBox
function CustomSearchBox() {
  const { query, refine } = useSearchBox();
  const [isBackspacePressed, setIsBackspacePressed] = useState(false);
  const [text, setText] = useState('');
   const history = useHistory();
  const handleChange = (event) => {
    setText(event.target.value)
    refine(event.currentTarget.value);
  };
  const handleKeyDown = (e) => {
    //console.log('User pressed: ', e.key);

     if (e.key === "Backspace") {
        setIsBackspacePressed(true);
      } else {
        setIsBackspacePressed(false);
      }
    
      // const handleSearchFormValidation = () => {
      //   return text.trim() !== '';
      // };
      // if (handleSearchFormValidation()) {
      //   refine('');
      //   history.push(`/search/${text}`);
      // }
  };

  const handleOnSearchSubmit = (e)=> {
    e.preventDefault();
    const handleSearchFormValidation = () => {
      return text.trim() !== '';
    };

    if (handleSearchFormValidation()) {
      refine('');
      history.push(`/search/?q=${text}`);
    }
  }

  return (
    <div className="search_box_area">
    <form onSubmit={handleOnSearchSubmit}>
    <input
      type="text"
      value={query}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      placeholder="búsqueda"
      
      style={{ padding: '8px', width: '100%', fontSize: '16px' }}
    />
     <button type="submit">
      <img
        src={`${process.env.PUBLIC_URL}/images/new-images/icon/search-icon.svg`}
        alt="Header Search Icon"
        width="17"
        height="17"
      />
    </button>
    </form>
    </div>
  );
}

 export default function  ProductSearchAutoComplete() {
    // need to be inside InstantSearch
   return (
    <InstantSearch searchClient={searchClient} indexName="articles">
      <CustomSearchBox searchAsYouType={false} />
      <Configure  hitsPerPage={5}  filters="vencimiento=0"/>
      <CustomInfiniteHits/>
        
    </InstantSearch>
   );
 }
