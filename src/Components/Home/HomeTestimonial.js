import React, { Component } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

class TestimonialSlider extends Component {
  render() {
    const settings = {
      dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    centerMode: true,
    centerPadding: "0px",
    arrows: true,
    };

    return (
      <div className="testimonial-section">
        <h2 className="testimonial-heading">What People Are Saying About Us</h2>
        <div className="testimonial-slider-container">
          <Slider {...settings}>
            <div>
              <div className="testimonial-card">
                <div className="quote-icon">
                    <img src="/images/quote-icon.png" alt="Quote Icon" />
                </div>
                <p className="testimonial-title">
                </p>
                <p className="testimonial-description">
                  Me atendi<PERSON>, excelente vendedor, su atención fue increíble y nos consiguió todo lo que necesitábamos. Bastante amable y atento.
                </p>
                <div className="testimonial-user">
                  <img
                    src="https://lh3.googleusercontent.com/a-/ALV-UjVP3cHZ-h0YKURKo4BXyQQIKd0_3CqDu2kM90J0itWPBsGXYmqg=w72-h72-p-rp-mo-ba2-br100"
                    alt="Sarah Johnson"
                    className="testimonial-avatar"
                  />
                  <div>
                    <div className="testimonial-name">Eva Stamp</div>
                    <div className="testimonial-role"></div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="testimonial-card">
                <div className="quote-icon">
                    <img src="/images/quote-icon.png" alt="Quote Icon" />
                </div>
                <p className="testimonial-title">
                </p>
                <p className="testimonial-description">
                  Los precios son muy competitivos, tienen respaldos de garantía y la atención de sus vendedores especialmente de Jesús fue estupenda.
                </p>
                <div className="testimonial-user">
                  <img
                    src="https://lh3.googleusercontent.com/a-/ALV-UjVWfbojRcmHs8_OBnpl2nTd6RKn3bhnMqW0cbQmyxbA_hg_EmDpdA=w36-h36-p-rp-mo-br100"
                    alt="Sarah Johnson"
                    className="testimonial-avatar"
                  />
                  <div>
                    <div className="testimonial-name">angelica quiceno</div>
                    <div className="testimonial-role"></div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="testimonial-card">
                <div className="quote-icon">
                    <img src="/images/quote-icon.png" alt="Quote Icon" />
                </div>
                <p className="testimonial-title">
                </p>
                <p className="testimonial-description">
                  Excelente atención y servicio. Muy amable el personal, buenos precios
                </p>
                <div className="testimonial-user">
                  <img
                    src="https://lh3.googleusercontent.com/a-/ALV-UjWn0xbYrE_NTd9iNz3Tl0NhpFTZnsc7rc8x3R7C_iZAJaVrdsg=w72-h72-p-rp-mo-br100"
                    alt="Sarah Johnson"
                    className="testimonial-avatar"
                  />
                  <div>
                    <div className="testimonial-name">Dayanara Gonzalez</div>
                    <div className="testimonial-role"></div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="testimonial-card">
                <div className="quote-icon">
                    <img src="/images/quote-icon.png" alt="Quote Icon" />
                </div>
                <p className="testimonial-title">
                </p>
                <p className="testimonial-description">
                  Muy buena atención y asesoría a lo largo del curso en la tienda
                </p>
                <div className="testimonial-user">
                  <img
                    src="https://lh3.googleusercontent.com/a-/ALV-UjUre9I8jGByoaHNTWVMaRZW1bGIRwIzFvSaE56-m-eEv0EM93j4=w72-h72-p-rp-mo-br100"
                    alt="Sarah Johnson"
                    className="testimonial-avatar"
                  />
                  <div>
                    <div className="testimonial-name">Ignacio Pereira</div>
                    <div className="testimonial-role"></div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="testimonial-card">
                <div className="quote-icon">
                    <img src="/images/quote-icon.png" alt="Quote Icon" />
                </div>
                <p className="testimonial-title">
                </p>
                <p className="testimonial-description">
                  Variedad de marcas, productos y excelente atención, mucha empatía, muy buena demostración del producto.
                </p>
                <div className="testimonial-user">
                  <img
                    src="https://lh3.googleusercontent.com/a-/ALV-UjWsDy-Fjr-u-Rx7fqbLQtqfbwR9IjURlkVpk2DCZADsnenlwA8=w72-h72-p-rp-mo-ba2-br100"
                    alt="Sarah Johnson"
                    className="testimonial-avatar"
                  />
                  <div>
                    <div className="testimonial-name">mabel cedeno</div>
                    <div className="testimonial-role"></div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="testimonial-card">
                <div className="quote-icon">
                    <img src="/images/quote-icon.png" alt="Quote Icon" />
                </div>
                <p className="testimonial-title">
                </p>
                <p className="testimonial-description">
                  Muy amables y una gran atención
                </p>
                <div className="testimonial-user">
                  <img
                    src="https://lh3.googleusercontent.com/a/ACg8ocIAwggqKk6IF7NqptFCOsn4txIXiNz23RQzkFYBWmZoWnfwOg=w72-h72-p-rp-mo-br100"
                    alt="Sarah Johnson"
                    className="testimonial-avatar"
                  />
                  <div>
                    <div className="testimonial-name">JoseAngel Jaen</div>
                    <div className="testimonial-role"></div>
                  </div>
                </div>
              </div>
            </div>
          </Slider>
        </div>
      </div>
    );
  }
}

export default TestimonialSlider;
