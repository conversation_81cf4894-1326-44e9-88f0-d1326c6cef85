.mobile-modal-dialog {
    .modal-dialog {
        margin: 0;
        height: 100%;
        max-width: 100%;
        h2 {
            font-size: 20px;
            line-height: 25px;
            color: #131921;
            font-weight: 500;
            margin-bottom: 5px;
        }
        h3 {
            font-size: 15px;
            line-height: 22px;
            color: #7D899D;
            font-weight: 400;
        }
    }
    .modal-content {
        border-radius: 0;
        height: 100%;
    }
    .modal-body {
        overflow-y: auto;
    }
    .popupbg {
        border-radius: 0;
    }
    .input-wrap {
        margin-bottom: 15px;
       input {
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        color: rgba(125, 137, 157, 1);
        height: 48px;
        border: 1px solid #C1CDD9;
        border-radius: 8px;
        padding-left: 15px;
        box-shadow: inset 0 0 5px 0 rgba(219, 230, 241, 1);
        &:focus {
            border: 1px solid #0075B9;
            box-shadow: 0 0 0 5px rgba(0,117,185,0.1);
        }
       }
    }
    .popupbg {
        .signup-btn-wrap,
        .reg-signup-btn {
            .btn {
                margin: 15px auto 15px;
                background: #0075B9;
                border-radius: 8px !important;
                margin-bottom: 25px;
            }
        }
    }
    .reg-btn-wrap {
        p {
            font-size: 15px;
        }
        .btn {
            font-size: 15px !important;
        }
    }
    .right-form-logo {
        img {
            width: 120px;
        }
    }
    .no-account {
        text-decoration: underline;
    }
}

.right-form-graphic {
    text-align: center;
    img {
        margin: 40px 0;
    }
}

.right-form-area {
    .input-label {
        font-size: 14px;
        line-height: 21px;
        color: #131921;
        margin-bottom: 5px;
    }
}

.remember-forget {
    display: flex;
}

.remember-me {
    flex: 1;
    label {
        display: flex;
        width: 135px;
        font-size: 14px;
        font-weight: 400;
        color: #0075B9;
        margin-bottom: 0;
    }
    input {
        width: 14px;
        margin-right: 5px;
        border: 1px solid #C1CDD9;
        margin-bottom: 0;
        margin-top: 0;
    }
}



.mobile-popupbg {
    position: relative;
    &::before {
        content: '';
        position: absolute;
        background: #ECF2F8;
        height: 1px;
        width: 100%;
        top: 60px;
        left: 0;
        right: 0;
    }
}

.mobile-logo-login {
    .right-form-logo {
        padding: 15px 0 0;
        padding-top: 15px;
    }
}