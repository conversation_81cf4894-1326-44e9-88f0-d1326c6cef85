.product-view-container--mobile {
  .container {
    padding: 0;
  }
  .exzoom_img_ul {
    .carousel-root {
      position: relative;
      .carousel-slider {
        .slide {
          border-radius: 0;
          background: rgba(45, 91, 165, 0.15);
        }
      }
      .carousel + .carousel {
        position: absolute;
        bottom: 25px;
        left: 0;
        right: 0;
        text-align: center;
        margin: 0 auto;
        max-width: 220px;
        .thumbs-wrapper {
          margin: 0;
          .control-arrow {
            display: none;
          }
        }
      }
      .thumbs.animated {
        justify-content: center;
        overflow: hidden;
      }
      .thumb {
        width: 46px !important;
        height: 46px !important;
        flex: 0 0 46px !important;
        border-radius: 12px !important;
        padding: 0 !important;
        align-items: center;
        display: flex;
        &:hover {
          border: #dfe6f1 !important;
        }
        .selected {
          border: none;
        }
        img {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  .details-container {
    padding: 0 15px;
    margin-top: 25px;
    h2 {
      font-size: 18px;
      line-height: 22px;
      font-weight: 500;
      color: #131921;
      text-transform: none;
      margin-bottom: 10px;
    }
  }
  .review-rating {
    align-items: center;
    border-bottom: 0;
    padding: 0;
    margin-bottom: 10px;
    p {
      color: #fd9800;
      font-size: 14px;
      text-decoration: underline;
    }
    .star {
      height: 16px;
      .staricon {
        font-size: 16px;
        margin-right: 0;
      }
    }
  }
  .price-tag {
    margin-bottom: 5px;
    .price {
      font-size: 20px;
      line-height: 20px;
      font-weight: 600;
      color: #131921;
      padding-right: 15px;
      margin-right: 10px;
      border-color: #cfd7e5;
    }
  }
  .buy-now {
    border-radius: 8px;
    font-size: 16px !important;
    font-weight: 400 !important;
    text-transform: capitalize;
  }
  .offer-badge {
    position: absolute;
    top: 20px;
    left: 25px;
    z-index: 9;
    background: #219653;
    border-radius: 4px;
    color: #fff;
    font-size: 10px;
    padding: 3px 7px;
  }
  .product-action-tool {
    position: absolute;
    top: 20px;
    right: 32px;
    z-index: 9;
  }
  .product-wishlist {
    margin-bottom: 15px;
  }
  .bottom-actions {
    justify-content: space-between;
    gap: inherit;
    .whatsapp {
      margin-left: 0;
      padding: 3px 11px;
    }
    .qty {
      margin-right: 0;
      max-width: 110px;
      padding-left: 10px;
      padding-right: 10px;
      input[type="text"] {
        width: 50px;
        font-size: 17px;
      }
    }
    button {
      min-width: 170px;
      font-size: 14px;
    }
  }
  .card-desc {
    padding: 0 15px;
  }
  .bac_credometic_span {
    padding: 7px 20px;
    font-size: 14px;
    background: rgba(223, 230, 241, 0.6);
    color: rgba(60, 82, 93, 0.9);
  }
  .cmn-block-border {
    border-bottom: 4px solid #ECF2F8;
    padding-bottom: 20px;
  }
  .card-block {
    h6 {
        font-size: 16px;
        line-height: 20px;
        font-weight: 500;
        color: #131921;
    }
    h5 {
        font-size: 15px;
        line-height: 21px;
        font-weight: 400;
        color: #7D899D;
    }
  }
  .more-info {
    padding: 25px 15px 0;
    h6 {
        margin-bottom: 20px;
    }
    ul {
        li {
            padding-left: 35px;
            margin-bottom: 18px;
            position: relative;
            color: #7D899D;
            font-size: 15px;
            line-height: 21px;
            @media (min-width: 767px) {
              margin-bottom: 20px;
              padding-left: 30px;
            }
            &:before {
                content: '';
                position: absolute;
                top: 2px;
                left: 0;
                background: url(../images/list-icons.svg) no-repeat;
                background-size: 100% 100%;
                background-position: center;
                width: 20px;
                height: 20px;
                @media (min-width: 767px) {
                  top: 0;
                  width: 22px;
                  height: 22px;
                }
            }
        }
    }
  }
  .review-rating-info {
    margin-top: 0;
    padding: 0;
  }
  .descripcions {
    .review-tittle {
        flex-direction: row;
        flex-wrap: nowrap;
        padding: 15px 15px;
        .review-count {
            border-top: none;
            padding-left:15px;
            padding-bottom: 0;
            padding-top: 0;
            margin: 0;
            @media (max-width: 767px) {
              font-size: 14px;
              line-height: 26px;
              color: #3C525D;
              display: flex;
              align-items: center;
            }
        }
    }
    h3 {
        font-size: 16px !important;
        line-height: 20px !important;
        font-weight: 500;
        color: #131921;
        border-right: 1px solid #CFD7E5 !important;
        padding-right: 15px;
    }
    .review-box {
        padding: 0 15px;
        .namearea {
            flex: 1;
        }
    }
    .review-box__rating {
        .stararea {
            margin-right: 10px;
            .staricon {
                width: 20px;
                height: 20px;
            }
        }
        span {
            flex: 1;
            width: 20px;
            height: 20px;
        }
    }
  }
}
