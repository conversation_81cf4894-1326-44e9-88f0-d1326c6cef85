import React, { Component } from "react";
import BannerSection from "./BannerSection";
import BreadcrumbSection from "./BreadcrumbSection";
import CheckoutListingSection from "./CheckoutListingSection";
import MobileCheckoutListingSection from "./MobileCheckoutListingSection";

import { isMobile } from "react-device-detect";
import { MobileHeaderSecondary } from "../Shared/MobileHeaderSecondary";
import "./CheckoutPage.module.css";

export default class CheckoutPage extends Component {
  componentDidMount() {
    window.Intercom("update", {
      hide_default_launcher: true,
    });
  }

  componentDidMount() {
    document.body.classList.add("hide-header-mobile");
  }

  componentWillUnmount() {
    document.body.classList.remove("hide-header-mobile");
  }
  renderContent() {
    if (isMobile) {
      return (
        <div className="">
          {/* <BannerSection />
                    <BreadcrumbSection /> */}
          <MobileHeaderSecondary
            title={"Checkout"}
            hasSearch={false}
            link={"/cart"}
          />
          <MobileCheckoutListingSection />
        </div>
      );
    }
    return (
      <div className="">
        <BannerSection />
        <BreadcrumbSection />
        <CheckoutListingSection />
      </div>
    );
  }
  render() {
    return this.renderContent();
  }
}
