{"name": "myfirstreact", "homepage": "https://www.evisionstore.com/", "version": "0.1.0", "private": true, "dependencies": {"@brainhubeu/react-carousel": "^1.19.26", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "algoliasearch": "^4.17.2", "axios": "^0.19.2", "bootstrap": "^4.4.1", "google-maps-react": "^2.0.6", "jquery": "^3.6.0", "multiselect-react-dropdown": "^1.5.7", "public-ip": "^4.0.3", "query-string": "^6.14.1", "react": "^16.13.0", "react-bootstrap": "^1.0.0-beta.17", "react-bootstrap-carousel": "^4.0.3", "react-device-detect": "^1.11.14", "react-dom": "^16.13.0", "react-helmet": "^6.1.0", "react-image-magnifiers": "^1.4.0", "react-image-magnify": "^2.7.4", "react-inner-image-zoom": "^3.0.1", "react-instantsearch": "^7.15.7", "react-instantsearch-hooks-web": "^6.47.3", "react-intl-tel-input": "^8.0.4", "react-loading-skeleton": "^2.1.1", "react-redux": "^7.2.0", "react-responsive-carousel": "^3.2.11", "react-router-dom": "^5.1.2", "react-scripts": "3.4.0", "react-slick": "^0.25.2", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "sass": "^1.69.0", "slick-carousel": "^1.8.1"}, "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}