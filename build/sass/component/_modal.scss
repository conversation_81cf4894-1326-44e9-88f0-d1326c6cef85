.modal {
  z-index: 99999999999 !important;
  color: #3C525D;
}
.modal-dialog {
  width: 820px;
  margin: 30px auto;
  .mobile-logo-login{
    float: none;
  }
  .close {
    float: none !important;
    width: 40px !important;
    height: 40px !important;
    cursor: pointer;
    font-size: 40px !important;
    font-weight: $font-weight-thin;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=30);
    opacity: 0.3;
    margin: 9px;
    position: absolute !important;
    right: 0 !important;
    z-index: 999999;
  }
  h2 {
    font-size: 25px;
    line-height: 27px;
    font-weight: 600;
    color: #3C525D;
    text-align: center;
    margin: 10px 0;
  }
  h3 {
    font-size: 18px;
    font-weight: 400;
    color: #3C525D;
    text-align: center;
    margin: 0px 0 20px 0;
  }
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 0 !important;
  .wraparea{
    max-width: 340px;
    width: 100%;
    text-align: left;
    margin: auto;
    .input-text{
      border: none;
      border-bottom: 1px solid #CFD7E5;
      color: #3C525D;
      font-size: 13px;
      line-height: 27px;
      margin-bottom: 0;
      padding: 10px 10px 10px 27px;
      border-radius: 0px;
      background-color: transparent;
    }
  }
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: none !important;
  border-radius: 5px;
  outline: 0;
  overflow: hidden;
}
.modal-close {
  position: absolute;
  top: 0;
  right: 0;
}
.right-form-area {
  text-align: left;
  padding-left: 44px;
  float: left;
  label {
    display: inline-block;
    width: 100%;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 18px;
    color: #4f4f4f;
    margin-bottom: 10px;
  }
  input,
  select,
  textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    width: 100%;
    border: 1px solid #CFD7E5;
    border-radius: 5px;
    padding: 5px;
    margin-bottom: 15px;
  }
}
.right-form-logo {
  text-align: center;
  margin: auto;
  max-width: 200px;
  padding: 30px 0 20px 0;
}
.login-modal {
  .modal-content {
    padding: 50px 0;
    border-radius: 25px;
    .modal-body {
      position: static;
    }
  }
}
.left-login-box {
  padding: 0px 30px;
  border-right: $separator-color 1px solid;
}
.right-login-box {
  padding-right: 80px;
  padding-left: 80px;
  float: none;
  h2{
    text-align: left;
    @media (max-width: 992px) {
      text-align: center;
    }
  }
}
.right-login-form-logo {
  text-align: left;
  padding-top: 10px;
  padding-bottom: 10px;
  h2 {
    font-size: 25px;
    line-height: 27px;
    color: $primary-color;
    margin-top: 25px;
    margin-bottom: 8px;
    font-weight: $font-weight-semibold;
  }
  p {
    font-size: 15px;
    line-height: 23px;
    font-weight: $font-weight-medium;
    color: $primary-color;
  }
}

.login-form-wrap {
  input {
    border-radius: 0;
    border-top: 0;
    border-left: 0;
    border-right: 0;
  }
}


.popupbg {
  //background: #fff url(../images/popupbg.png) no-repeat -2px top !important;
  //background-size: cover;
  min-height: 537px;
  float: left;
  width: 100%;
  border-radius: 16px;

  .text-center-middle{
    margin: auto;
    justify-content: center;
  }

  .signup-btn-wrap{
    .btn{
      max-width: 280px;
      padding: 10px;
      float: none;
      margin: 30px auto 17px;
      @media (max-width: 767px) {
        max-width: 100%;
        width: 100% !important;
      }
      img{
        width: 12px;
        margin-left: 8px;
      }
    }
  }

}

.forgot-remember{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  .remember-me{
    color: $primary-color;
    font-size: 13px;
    line-height: 27px;
    font-weight: 400;
    text-transform: capitalize;
  }
  .form-check{
    padding: 0;
    display: inline-block;
    input{
      margin:0 12px 0 0;
      border-color: $primary-color;
      width: 15px;
      height: 15px;
      position: relative !important;
      top: 3px;
    }
  }
  .no-account{
    font-size: 13px;
    line-height: 27px;
    color: $primary-color;
    text-decoration: underline;
    text-transform: capitalize;
  }
}

.login-form-wrap{
  input{
    border-bottom: 1px solid #CFD7E5;
    color: $primary-color;
    font-size: 13px;
    line-height: 27px;
    margin-bottom: 0;
    padding: 10px 10px 10px 27px;
    border-radius: 0px;
    &::placeholder {
      color: #76868e;
      opacity: 1;
    }
    
    &::-ms-input-placeholder {
      color: #76868e;
    }
  }
}

.signup-btn-wrap{
  display: block;
  .btn{
    color: #fff;
    width: 100%;
    background-color: #0075B9;
    border-radius: 50px !important;
    font-size: 16px !important;
    line-height: 24px;
    font-weight: 400;
    text-transform: capitalize;
    padding: 13px 15px !important;
    svg{
      margin-left: 10px;
    }
  }
}

.reg-btn-wrap{
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  p{
    color: $primary-color;
    font-size: 13px;
    line-height: 27px;
    margin: 0;
  }
  .btn{
    background: transparent;
    font-size: 13px !important;
    line-height: 27px;
    color: #1675b9;
    font-weight: 400;
    margin: 0 0 0 5px !important;
    padding: 0 !important;
    text-decoration: underline;
    width: auto;
    float: none;
    text-transform: capitalize;
    outline: none;
    &:hover{
      background: transparent;
      color: #fe0000;
    }
  }
}

.accept-condition{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: $primary-color;
  font-size: 13px;
  line-height: 27px;
  font-weight: 400;
  margin-bottom: 30px;
  .form-check{
    padding: 0;
    display: inline-block;
    input{
      margin:0 12px 0 0;
      border-color: $primary-color;
      width: 15px;
      height: 15px;
      position: relative !important;
      top: 3px;
    }
  }
  a{
    display: inline-block;
    margin-left: 5px;
  }
}

.reg-signup-btn{
  width: 100%;
  .btn{
    width: 100%;
    background: #0075B9;
    border-radius: 50px !important;
    font-size: 16px !important;
    padding: 13px 15px !important;
    line-height: 24px;
    font-weight: 400;
    text-transform: capitalize;
    color: #fff;
    padding: 10px 0;
    text-decoration: none;
    &:hover{
      background: #fe0000;
      color: #fff;
    }
    svg{
      margin-left: 10px;
    }
  }
}
.reg-signin-btn{
  display: flex;
  margin-top: 20px;
  p{
    color: $primary-color;
    font-size: 13px;
    line-height: 27px;
    margin: 0;
  }
  .btn{
    background: transparent;
    font-size: 13px !important;
    line-height: 27px;
    color: #1675b9;
    font-weight: 400;
    margin: 0 0 0 5px;
    padding: 0 !important;
    text-decoration: underline;
    width: auto;
    float: none;
    text-transform: capitalize;
    outline: none;
    &:hover{
      background: transparent;
      color: #fe0000;
    }
  }
}
.registration_form {
  .form-wrap{
    input{
      border:none;
      border-bottom: 1px solid #CFD7E5;
      color: $primary-color;
      font-size: 13px;
      line-height: 27px;
      margin-bottom: 0;
      padding: 10px 10px 10px 27px;
      border-radius: 0px;
      &::placeholder {
        color: #76868e;
        opacity: 1;
      }
      
      &::-ms-input-placeholder {
        color: #76868e;
      }
    }
  }
}
.forgot_pass_form{
  .form-wrap{
    input{
      border:none;
      border-bottom: 1px solid #CFD7E5;
      color: $primary-color;
      font-size: 13px;
      line-height: 27px;
      margin-bottom: 0;
      padding: 10px 10px 10px 27px;
      border-radius: 0px;
      &::placeholder {
        color: #76868e;
        opacity: 1;
      }
      
      &::-ms-input-placeholder {
        color: #76868e;
      }
    }
  }
  .no-account{
    display: inline-block;
    font-size: 13px;
    color: #1675b9;
    font-weight: 400;
    text-decoration: underline;
    margin-top: 20px;
  }
}

.login-form-wrap{
  position: relative;
}
.input-wrap{
  position: relative;
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;
  .login-icon{
    position: absolute;
    left: 0;
    top:50%;
    transform: translateY(-50%);
    z-index: 1;
  }
}
.intl-tel-input.allow-dropdown.separate-dial-code .selected-flag{
  background-color: transparent !important;
  display: flex !important;
  align-items: center;
  justify-content: flex-end;
  width: 70px !important;
  margin-left: 20px;
}
.iti-flag{
  margin: 0 !important;
}
.flag-container .arrow {
  border: none !important;
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(0deg);
}
.input-wrap{
  input{
    border:none;
    border-bottom: 1px solid #CFD7E5;
    color: $primary-color;
    font-size: 13px;
    line-height: 27px;
    margin-bottom: 0;
    padding: 10px 10px 10px 27px;
    border-radius: 0px;
    background-color: transparent;
    &::placeholder {
      color: #3C525D;
      opacity: 1;
    }
    
    &::-ms-input-placeholder {
      color: #3C525D;
    }
  }
  textarea{
    border:none;
    border-bottom: 1px solid #CFD7E5;
    color: $primary-color;
    font-size: 13px;
    line-height: 27px;
    margin-bottom: 0;
    padding: 10px 10px 10px 27px;
    border-radius: 0px;
    background-color: transparent;
    resize: none;
    height: 50px !important;
    outline: none;
    &:hover{
      background: none;
    }
    &:focus{
      border-color: #CFD7E5;
      box-shadow: none;
      background: transparent;
    }
    &::placeholder {
      color: #3C525D;
      opacity: 1;
    }
    
    &::-ms-input-placeholder {
      color: #3C525D;
    }
  }
}

.margin-auto-top{
  margin: 20px auto 0 !important;
  text-align: center !important;
}

.no-account{
  font-size: 14px;
  color: #0075B9;
}
.continue-guest{
  h4{
    color: #3C525D;
    font-size: 18px;
  }
  p{
    color: #3C525D;
    font-size: 14px;
  }
  .btn{
    border-radius: 50px;
    padding: 10px 20px;
    img{
      width: 11px;
      margin-left: 8px;
    }
  }
}

.content-box{
  .model-tittle{
    color: #3C525D;
  }
  .model-body {
    padding: 30px;
  }
  p{
    font-size: 14px;
    color: #3C525D;
  }
  .text-padding{
    padding: 30px;
    text-align: center;
  }
}

.modal-login-screen{
  padding: 20px 0 45px;
  margin: 0;
  @media (max-width: 992px) {
    padding: 20px 0;
    margin: auto;
  }
}
.mobile-logo-login{
  @media (max-width: 767px) {
    .right-form-logo{
      max-width: 100%;
    }
    h3{
      br{
        display: none;
      }
    }
  }
}