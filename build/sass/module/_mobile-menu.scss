.mobile {
  .navbar-default {
    .navbar-collapse {
      position: fixed;
      top: 0;
      width: 100%;
      height: 100vh;
      padding: 0 !important;
      transform: translateX(-100%);
      transition: 0.3s all ease-in-out;
      display: block !important;
      &.show {
        transform: translateX(0);
      }
      .close {
        position: absolute;
        top: 13px;
        right: 18px;
        font-weight: 300;
        color: #131921;
        opacity: 1;
        font-size: 26px;
        text-shadow: none;
        @media (max-width: 767px) {
          color: #fff;
        }
      }
    }
  }
  .header-two {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 99999999;
  }
  .navbar-nav {
    margin-top: 45px !important;
    li {
      padding-bottom: 40px;
      margin-right: 0;
      a {
        padding: 0 35px;
        font-size: 20px !important;
        font-weight: 400;
        color: #dfe7f4 !important;
        display: flex;
        align-items: center;
        &:focus {
          &::after {
            color: #dfe7f4;
          }
        }
        &:hover,
        &:focus {
          color: #dfe7f4 !important;
          background: none !important;
        }
        img {
          margin-right: 15px !important;
        }
      }
      .dropdown-toggle {
        &:after {
          right: 35px;
          position: absolute;
          transform: rotate(-90deg);
        }
      }
      .dropdown-toggle-multi {
        &:hover {
          background: none;
        }
      }
      ul {
        width: 100%;
        padding: 0;
        margin-top: 20px;
        li {
          padding: 10px 0;
          padding-left: 30px;
          a {
            font-size: 17px !important;
            font-weight: 400;
            color: #dfe7f4;
            padding: 0 !important;
          }
        }
      }
    }
  }
  .multi-level {
    position: fixed !important;
    // height: calc(100vh - 159px);
    height: 100vh;
    top: 0 !important;
    max-height: none;
    margin: 0 !important;
    padding: 50px 0 0 !important;
    transform: translateX(100%);
    transition: 0.3s all ease-in-out;
    background: #0075b9;
    display: block;
    visibility: hidden;
    overflow-y: auto;
    &.show {
      transform: translateX(0);
      visibility: visible;
    }
  }
}

.user-link {
  &:hover {
    text-decoration: underline;
  }
}

.navbar-user-wrapper {
  background: #fff;
  display: flex;
  align-items: center;
  padding: 17px 20px;
  position: relative;
  .navbar-user-img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .navbar-user-details {
    margin-left: 10px;
  }
  h4 {
    margin-bottom: 0;
    font-size: 18px;
    line-height: 25px;
  }
  p {
    margin-bottom: 0;
    font-size: 11px;
    line-height: 15px;
    color: #3c525d;
  }
}

.login-btn {
  position: absolute;
  bottom: 0;
  padding: 24px 35px;
  width: 100%;
  border-top: 1px solid #dfe7f4;
  li {
    list-style: none;
    display: flex;
    align-items: center;
    a {
      font-size: 20px !important;
      font-weight: 400;
      color: #dfe7f4;
      display: flex;
      align-items: center;
      img {
        margin-right: 20px !important;
      }
    }
  }
}

.multi-menu-back {
  position: relative;
  cursor: pointer;
  &::before {
    content: "←";
    position: absolute;
    width: 30px;
    height: 25px;
    border-radius: 5px;
    border: 1px solid #dfe7f4;
    padding: 0;
    top: -30px;
    left: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 0;
    font-size: 24px;
    color: #0075b9;
    background: #dfe7f4;
  }
}

.section-top-tag--mobile {
  padding: 0 0 14px;
  border-color: #ecf2f8;
  ul {
    li {
      a {
        font-size: 13px;
      }
    }
  }
}
.navbar-user-wrapper-super + .close{
  @media (max-width: 767px) {
    color: #131921 !important;
  }
}