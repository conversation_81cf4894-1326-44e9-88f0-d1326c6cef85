// src/components/Search.jsx

import React from 'react';
import algoliasearch from 'algoliasearch/lite';
import {
  InstantSearch,
  SearchBox,
  Hits,
  Highlight,
  Configure
} from 'react-instantsearch-dom';

// Initialize Algolia client
const searchClient = algoliasearch(
  'YourApplicationID',        // Replace with your Algolia App ID
  'YourSearchOnlyAPIKey'      // Replace with your Search-Only API Key
);

const Hit = ({ hit }) => (
  <div className="hit-item">
    <img src={hit.image} alt={hit.name} width="50" />
    <div>
      <h4><Highlight attribute="name" hit={hit} /></h4>
      <p>${hit.price}</p>
    </div>
  </div>
);

const Search = () => {
  return (
    <div className="search-wrapper">
      <InstantSearch searchClient={searchClient} indexName="your_index_name">
        <Configure hitsPerPage={5} />
        <SearchBox
          translations={{ placeholder: 'Buscar productos...' }}
          className="search-box"
        />
        <div className="search-results">
          <Hits hitComponent={Hit} />
        </div>
      </InstantSearch>
    </div>
  );
};

export default Search;
