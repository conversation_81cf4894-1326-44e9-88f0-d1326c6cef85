.banner-container {
  width: 100%;
  min-height: 200px;
  margin: 10px 0 20px;

  @media (max-width: 1024px) {
    margin: 20px 0 0;
  }
  @media only screen and (max-width: 767px) {
    margin: 0px 0px;
    min-height: auto;
  }
  .container {
    @media (max-width: 767px) {
      padding: 0;
    }
  }
}

.banner-area-general {
  background-size: cover !important;
  height: 450px;
  padding: 5.5rem;
  border-radius: 16px;

  @media (max-width: 1024px) {
    height: auto;
    padding: 40px 15px;
  }

  @media (max-width: 767px) {
    border-radius: 0;
  }

  &--contact {
    background: url(../images/new-images/contact-banner.png) no-repeat center;
  }

  &--about {
    background: url(../images/new-images/about-banner.png) no-repeat center;
  }

  &--photo-catagory {
    background: url(../images/new-images/photo-catagory-banner.png) no-repeat
      center;
    .banner-title {
      text-transform: none;
    }
  }

  &--term {
    background: url(../images/header-bg.jpg) no-repeat center;
    display: flex;
    align-items: center;
    justify-content: center;
    .row {
      width: 100%;
    }
  }

  .banner-title {
    font-size: 44px;
    line-height: 60px;
    font-weight: 500;
    color: #fff;
    text-align: left;
    text-transform: capitalize;
    @media (max-width: 1024px) {
      font-size: 30px;
      line-height: 40px;
    }
    @media (max-width: 767px) {
      font-size: 26px;
      line-height: 34px;
    }
  }
  p {
    font-size: 15px;
    line-height: 23px;
    font-weight: 500;
    color: #fff;
    text-align: left;
    margin: 0;
    @media (max-width: 767px) {
      font-size: 13px;
      line-height: 20px;
    }
  }
}

.banner-area-inner {
  position: relative;
  background-size: cover !important;
  height: 300px;
  padding: 5.5rem;
  border-radius: 25px;
  overflow: hidden;

  @media (max-width: 1024px) {
    height: auto;
    padding: 40px 15px;
  }

  @media (max-width: 767px) {
    border-radius: 0;
  }

  .banner-area-inner-image {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .banner-title {
    font-size: 44px;
    line-height: 60px;
    font-weight: 500;
    color: #fff;
    text-align: left;
    text-transform: capitalize;
    @media (max-width: 1024px) {
      font-size: 30px;
      line-height: 40px;
    }
    @media (max-width: 767px) {
      font-size: 26px;
      line-height: 34px;
    }
  }
  p {
    font-size: 15px;
    line-height: 23px;
    font-weight: 500;
    color: #fff;
    text-align: left;
    margin: 0;
    @media (max-width: 767px) {
      font-size: 13px;
      line-height: 20px;
    }
  }
}

.breadcum {
  margin: 25px 0 0;
  @media (max-width: 767px) {
    padding: 0 17px;
  }
  a {
    font-size: 12px;
    color: #47545b;
    &:last-child {
      color: #0075b9;
    }
  }
}

.contact-box {
  background-color: #f0faff;
  border: 1px solid #cde0ea;
  border-radius: 16px;
  overflow: hidden;

  @media (max-width: 767.98px) {
    & {
      // margin: 30px 0;
      border: none;
      border-radius: 0;
    }
  }

  &__area {
    padding: 100px 70px;

    @media (max-width: 1024px) {
      padding: 30px 40px;
    }

    @media (max-width: 767.98px) {
      & {
        padding: 0;

        h5 {
          padding: 16px;
          margin: 0;
          font-size: 16px;
          line-height: 22px;
          border-bottom: solid 1px #dfe7f4;
        }

        form {
          padding: 8px 0 16px;
        }

        .form-group {
          padding: 8px 16px;
          margin: 0;

          .input-wrap {
            margin: 0;

            .login-icon {
              display: none;
            }

            input,
            textarea {
              background: #fff;
              border: solid 1px #c1cdd9;
              padding: 0;
              font-size: 14px;
              line-height: 22px;
              padding: 12px 16px;
              border-radius: 8px;
              height: auto;
            }

            textarea {
              height: 96px !important;
            }
          }

          .btn.btn-success {
            width: 100%;
            max-width: 100%;
          }

          label {
            font-size: 14px;
            line-height: 20px;
            font-weight: 500;
          }
        }
      }
    }

    .btn-success {
      background-color: #0075b9;
      border-color: #0075b9 !important;
      border-radius: 44px !important;
      font-size: 14px;
      font-weight: 600;
      max-width: 153px;
      width: 100%;
      padding: 10px;

      img {
        margin-left: 7px;
      }
    }
    a.whatsapp {
      border: none;
      outline: 0;
      padding: 5px 13px;
      color: white;
      background-color: #33d951;
      text-align: center;
      cursor: pointer;
      width: auto;
      font-size: 24px;
      border-radius: 50%;
      margin-left: 30px;
      top: 4px;
      position: relative;
    }
  }

  &__right-text {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    //background: url(../images/new-images/footer-right-back.png) no-repeat center;
    background-size: cover;
  }

  &__addressbar {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    background-color: #0075b9;
    color: #fff;
    border-top-right-radius: 16px;
    padding: 30px;
    max-width: 454px;
    width: 100%;
    justify-content: space-between;
    @media (max-width: 767px) {
      border-top-right-radius: 0px;
    }

    h5 {
      color: #fff;
      font-size: 20px;
      line-height: 25px;
      font-weight: 600;
      width: 100%;
    }
  }

  &__bararea {
    width: 35%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 7px;

    &.w70 {
      width: 55%;
    }
    p {
      font-size: 12px;
      line-height: 19px;
      font-weight: 500;
      margin: 0;

      a {
        color: #fff;
        display: block;
      }
    }
  }
}

.page-contact {
  @media (min-width: 768px) {
    .form-and-map {
      .info {
        display: none;
      }
    }
  }

  @media (max-width: 767.98px) {
    .form-and-map {
      padding: 0;

      iframe {
        height: 300px;
      }

      .info {
        background: #0075b9;
        border-radius: 32px 32px 0 0;
        padding: 32px 24px 20px;
        margin-top: -20px;
        position: relative;
        z-index: 1;

        & * {
          color: $white;
        }

        p {
          margin: 0 0 12px;
          font-size: 12px;
          line-height: 18px;
          font-weight: 500;
          position: relative;
          padding-left: 32px;

          a {
            color: #fff;
            text-decoration: none;
          }

          img {
            position: absolute;
            left: 0;
            top: 2px;
          }
        }
      }
    }
  }
}

.line-footer {
  position: relative;
  z-index: 9;
  margin-top: 60px;
  @media (max-width: 767px) {
    margin-top: 0;
  }
  hr {
    position: relative;
    width: 100%;
    height: 1px;
    margin: 0;
    background-color: #cfd7e5;
    top: 25px;
    @media (max-width: 767px) {
      top: 0;
    }
  }
}

.about-content-area {
  h4 {
    width: 100%;
    position: relative;
    color: #3c525d;
    font-size: 25px;
    line-height: 27px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 15px;

    @media (max-width: 767px) {
      font-size: 16px;
      line-height: 22px;
      font-weight: 500;
      color: #131921;
      padding: 0;
      margin: 0 0 8px;
    }
    &:after {
      position: absolute;
      content: "";
      width: 140%;
      height: 1px;
      background-color: #cfd7e5;
      left: inherit;
      bottom: 0;
      right: 0;

      @media (max-width: 767px) {
        width: 100%;
        left: 0;
        right: 0;
        bottom: 0;
        display: none;
      }
    }
  }
  p {
    color: #3c525d;
    font-size: 15px;
    line-height: 27px;
    font-weight: 400;
    margin-bottom: 25px;

    @media (min-width: 768px) {
      &:last-child {
        margin-bottom: 0;
      }
    }

    @media (max-width: 767px) {
      & {
        color: #7d899d;
        margin-bottom: 12px;
      }
    }
  }
  img {
    border-radius: 30px;
    position: relative;
    z-index: 1;

    @media (max-width: 767px) {
      margin: 15px 0;
      border-radius: 20px;
    }
  }
  .margin-spacing {
    margin-top: 70px;
    @media (max-width: 767px) {
      // margin-top: 30px;
      margin-top: 0px;
    }
    h4 {
      &:after {
        left: 0;
        bottom: 0;
        right: inherit;
      }
    }
  }
}

// .about-content-area {
//   @media (max-width: 767px) {
//     & {
//       margin-bottom: 20px;
//     }
//   }
// }

.general-content {
  margin-top: 30px;
  p {
    font-size: 15px;
    line-height: 27px;
    color: #3c525d;
  }
  h4 {
    font-size: 22px;
    line-height: 32px;
    color: #3c525d;
    font-weight: 500;
  }
  h5 {
    font-size: 18px;
    line-height: 26px;
    color: #3c525d;
    font-weight: 600;
  }
  ul {
    list-style: disc;
    padding-left: 30px;
    margin-bottom: 30px;
    li {
      font-size: 15px;
      line-height: 27px;
      color: #3c525d;
    }
  }
}
.general-line {
  hr {
    position: relative;
    width: 100%;
    height: 1px;
    margin: 50px 0 20px;
    background-color: #cfd7e5;
    @media (max-width: 767px) {
      margin: 30px 0 10px;
    }
  }
}

.branch-item {
  margin: 15px 0;
  width: 100%;
  display: inline-block;

  h4 {
    color: #000;
    margin-bottom: 20px;
  }

  .container {
    background: #f0faff;
    border-radius: 16px;
    overflow: hidden;
  }
  .cell-padding {
    width: 100%;
    display: inline-block;
    margin-bottom: 15px;
    p {
      margin: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .map-block {
    padding: 0;
    p {
      margin: 0;
    }
  }
  .left-text-padding {
    padding: 0 0 0 20px;
  }
}
