import React from "react";
import { Link } from "react-router-dom";

export const MobileProductListSidebar = ({
  categories,
  selectedCategoryId,
}) => {
  return (
    <div className="category-sidebar">
      {(categories?.length > 0 && (
        <ul>
          {categories?.map((category) => (
            <li key={category?.id}>
              <Link
                to={`/categorias/${category?.slug}`}
                className={
                  (category?.id === selectedCategoryId && "active") || ""
                }
              >
                <img src={category?.category_image} alt="" />
                <span>{category?.name}</span>
              </Link>
            </li>
          ))}
        </ul>
      )) ||
        null}
    </div>
  );
};
