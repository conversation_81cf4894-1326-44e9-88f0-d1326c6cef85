import React, {Component} from 'react';
import { Helmet } from 'react-helmet';
import API_BASE_URL from '../../config/api';

const TITLE = 'E-Vision / Políticas de Privacidad';

export default class PrivacyPolicy extends Component {
    constructor(props) {
        super(props);
        this.state = {
            privacyPolicy: ''
        }
    }

    componentDidMount() {
        //const apiUrl = 'https://www.evisionstore.com/api/home/<USER>';
        const apiUrl = `${API_BASE_URL}/privacy-policy`

        fetch(apiUrl)
        .then(res => res.json())
        .then(
            (result) => {
                this.setState({
                    privacyPolicy: result.data[0].page_contents
                });
            }
        )
    }

    render(){
        const { privacyPolicy } = this.state;
        return(
            <div className="wraper">
                <Helmet>
                    <title>{ TITLE }</title>
                </Helmet>
                <section className="banner-container">
                    <div className="container">
                        <div className="row">
                            <div className="col-md-12 p-md-0">
                                <div className="banner-area-general banner-area-general--term">
                                    <div className="row">
                                        <div className="col-lg-8">
                                            <h1 className="banner-title">Políticas de Privacidad</h1>  
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-md-12">
                                <p className="breadcum"><a href="javascript:void(0)">Home</a> <span>&#62;</span> <a href="javascript:void(0)">Privacy & Policy</a></p>
                            </div>
                        </div>
                    </div>		      
                </section>

                <div className="container general-content">
                    <div className="row">
                        <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12" dangerouslySetInnerHTML={{ __html: privacyPolicy }} />
                    </div>
                </div>
                
                <div className="general-line">
                    <div className="container">
                        <div className="row">
                            <div className="col-md-12">
                                <hr></hr>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        )
    }
}