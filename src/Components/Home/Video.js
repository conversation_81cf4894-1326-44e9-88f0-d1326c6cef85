import React, { Component } from 'react';
import { <PERSON> } from "react-router-dom";
import Slider from 'react-slick';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import API_BASE_URL from '../../config/api';

class ImageSlider extends Component {

    constructor(props) {
        super(props);
        this.state = {
            error: null,
            products: [],
            isProductLoaded: false,
        };
    }
    async componentDidMount() {
        const apiUrl = `${API_BASE_URL}/video-and-smartwatch-products`;
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({"categories": ["fotografia", "audio"]})
        };
        try {
            const response = await fetch(apiUrl, requestOptions);
            const result = await response.json();
            console.log(result.data);
            this.setState({
                //hotCategoryProducts: Object.values(result.data)[0],
                products: result.data,
                isProductLoaded: true,
            });
        } catch (error) {
            this.setState({ error: error });
        }
    }

    // Add this method to handle product redirection
    handleProductLink(product) {
        let productModel = product.modelo;
        if (productModel) {
            let checkSlash = "/";
            if (productModel.indexOf(checkSlash) !== -1) {
                productModel = product.modeloch;
            }
            return `/producto/${productModel}`;
        }
        return '#';
    }

    render() {
        const settings = {
            dots: false,
            arrows: true,
            infinite: true,
            speed: 500,
            slidesToShow: 6,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 3000,
            cssEase: "linear"
        };
        // Helper function to convert key like "video-juegos" to "Video Juegos"
        const formatTitle = (key) => {
            return key
            .replace(/-/g, ' ')
            .replace(/\b\w/g, (char) => char.toUpperCase());
        };
        return (
            <div>
                <section className="video-slide-section">
                {Object.entries(this.state.products).map(([categoryKey, categoryValue]) => {
                    const productCount = categoryValue.products.length;

                    // Dynamically adjust slider settings based on product count
                    const dynamicSettings = {
                        dots: false,
                        arrows: true,
                        infinite: productCount > 6,
                        speed: 500,
                        slidesToShow: Math.min(productCount, 6),
                        slidesToScroll: 1,
                        autoplay: productCount > 6,
                        autoplaySpeed: 3000,
                        cssEase: "linear"
                    };

                    return (
                        <div className={`container video-slide-section--container-${categoryKey}`} key={categoryKey}>
                            <div className="video-slide-section__top">
                                <h2>{formatTitle(categoryKey)}</h2>
                                {categoryValue.show_more && (
                                    <a href={`/categorias/${categoryKey}`}>
                                        Show More <img src="/images/circle-arrow.svg" alt="arrow" />
                                    </a>
                                )}
                            </div>

                            <Slider {...dynamicSettings}>
                                {categoryValue.products.map((product) => (
                                    <div key={product.product_id}>
                                        <Link to={this.handleProductLink(product)} rel="noopener noreferrer" className="video-slide-section__item">
                                            <div className="video-slide-section__img">
                                                <img
                                                    src={product.product_image}
                                                    alt={product.product_name}
                                                    style={{ width: '100%', height: 'auto', display: 'block' }}
                                                />
                                            </div>
                                            <h3>{product.product_name}</h3>
                                        </Link>
                                    </div>
                                ))}
                            </Slider>
                        </div>
                    );
                })}
            </section>
                <div className="container">
                    <section className="product-banner-area product-banner-area--top-spacing">
                        <Link to="/categorias/audio" className="product-banner-area__box product-banner-area__box4">
                            <img src="/images/product-img4.jpg" alt="Product Img" />
                            <h3>Audio Players</h3>
                        </Link>
                        <Link to="/categorias/linea-blanca" className="product-banner-area__box product-banner-area__box5">
                            <img src="/images/product-img5.jpg" alt="Product Img" />
                            <h3>Household Appliances</h3>
                            <p>Innovative Solutions for Efficient and Sustainable Living</p>
                        </Link>
                        <Link to="/categorias/drones" className="product-banner-area__box product-banner-area__box6">
                            <img src="/images/product-img6.jpg" alt="Product Img" />
                            <h3>Drones</h3>
                        </Link>
                    </section>
                </div>
            </div >
        );
    }
}

export default ImageSlider;