import React, { Component } from 'react';
import DealSlider from "react-slick";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import { Link } from 'react-router-dom';

export default class MobileTopTagSection extends Component {
    render() {
        const mobiletaginfo = {
            infinite: true,
            autoplay: true,
            speed: 500,
            slidesToShow: 3,
            slidesToScroll: 1,
            fade: false,
            variableWidth: true,
        };

        return (
            <section className="section-top-tag section-top-tag--mobile">
                <div className="container">
                            <ul> 
                            <DealSlider {...mobiletaginfo}>
                                <li><Link to="/">Deal of the day</Link></li>
                                <li>
                                    <Link to="/">
                                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                                        <g clipPath="url(#clip0_768_5846)">
                                        <path d="M1.48249 6.82773C1.48249 6.82773 2.0356 7.64031 3.19487 8.32811C3.19487 8.32811 2.35528 1.23236 7.82575 0C6.42419 5.14393 9.60856 6.5946 10.7693 3.87696C12.7078 6.46507 11.236 8.72307 11.236 8.72307C12.0308 8.83758 12.6985 7.9692 12.6985 7.9692C12.7045 8.07617 12.7078 8.18388 12.7078 8.29235C12.7078 11.4446 10.1524 14 7.00016 14C3.84789 14 1.29248 11.4446 1.29248 8.29232C1.29248 7.78597 1.35876 7.29515 1.48249 6.82773Z" fill="#FF6536"/>
                                        <path d="M12.6984 7.9692C12.6984 7.9692 12.0307 8.83758 11.2358 8.72307C11.2358 8.72307 12.7077 6.46507 10.7692 3.87696C9.60843 6.5946 6.42406 5.14393 7.82562 0C7.53263 0.0660078 7.25788 0.148941 7 0.246586V14C10.1523 14 12.7077 11.4446 12.7077 8.29232C12.7077 8.18385 12.7043 8.07617 12.6984 7.9692Z" fill="#FF421D"/>
                                        <path d="M4.49707 11.4973C4.49707 12.8798 5.61778 14.0005 7.00023 14.0005C8.38267 14.0005 9.50338 12.8798 9.50338 11.4973C9.50338 10.7574 9.18234 10.0924 8.67191 9.63419C7.70411 10.949 6.32495 8.95454 7.39977 7.49805C7.39977 7.49805 4.49707 7.8618 4.49707 11.4973Z" fill="#FBBF00"/>
                                        <path d="M9.50316 11.4973C9.50316 10.7574 9.18211 10.0924 8.67169 9.63419C7.70388 10.949 6.32472 8.95454 7.39955 7.49805C7.39955 7.49805 7.24183 7.51787 7 7.59419V14.0005C8.38245 14.0005 9.50316 12.8798 9.50316 11.4973Z" fill="#FFA900"/>
                                        </g>
                                        <defs>
                                        <clipPath>
                                        <rect width="14" height="14" fill="white"/>
                                        </clipPath>
                                        </defs>
                                    </svg>
                                    Hot Deals
                                    </Link>
                                </li>
                                <li><Link to="/">Best Sellers</Link></li>
                                <li><Link to="/">New Arrival</Link></li>
                            </DealSlider>
                            </ul>
                    </div>        
            </section>
        );
    }
}
