import React, { useEffect, useState } from "react";
import { Link, useParams, useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Modal, Form } from "react-bootstrap";
import Pagination from "./ProductPagination";
import { redirectTo } from "../Header/redirecturl";
import Skeleton from "react-loading-skeleton";


const AlgoliaProductRightCategoryList = () => {

  return (
        <>
        79
        </>
  );
};

export default AlgoliaProductRightCategoryList;
