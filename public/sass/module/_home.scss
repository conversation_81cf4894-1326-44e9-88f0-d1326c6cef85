body {
  font-family: $primary-font !important;
}
.modal-backdrop.show {
  opacity: 0.75 !important;
}
.banner-item {
  border-radius: 25px;
  overflow: hidden;
  a {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}
.banner-slider-container {
  margin-top: 15px;
  @media (max-width: 767px) {
    margin: 0px 0;
  }
  .slick-dots {
    bottom: 30px;
    li {
      button {
        padding: 0;
        &:before {
          content: "";
          width: 10px;
          height: 10px;
          border: $white 1.5px solid;
          border-radius: 100%;
          opacity: 1;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      &.slick-active {
        button {
          &:before {
            content: "";
            width: 26px;
            height: 7px;
            border: $white 1px solid;
            border-radius: 5px;
            opacity: 1 !important;
            background-color: $white;
          }
        }
      }
    }
  }
}

.slick-slide {
  z-index: 0; /* All slides start at z-index 0 */
  opacity: 0; /* Hidden by default */
  transition: opacity 1s ease, z-index 0s ease 1s; /* Fade and transition */
}

.slick-active {
  z-index: 1 !important; /* The active slide gets higher z-index */
  opacity: 1 !important; /* The active slide is fully visible */
  transition: opacity 1s ease; /* Fades in */
}

/* Add positioning to ensure proper layering */
.slick-list {
  overflow: visible; /* Make sure slides don’t get clipped */
}

.section-title {
  text-align: center;
}

.category-container {
  margin-top: 30px;
  margin-bottom: 50px;
  .department-link-item {
    figure {
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      border-radius: 12px;
      border: 1px solid #a8d2ea;
      background-color: #f0faff;
      width: 90%;
      height: 70px;
      max-width: 71px;
      padding: 0;
      img {
        width: 40px;
        height: auto;
      }
    }
  }
}

// ANCHOR Common Heading Title style

.common-section-title {
  position: relative;
  margin-bottom: 20px;
  @media only screen and (max-width: 767px) {
    margin-bottom: 15px;
  }
  // &:before {
  //   content: "";
  //   height: 2px;
  //   width: 40%;
  //   background: rgb(45, 91, 165);
  //   background: linear-gradient(90deg, rgba(45, 91, 165, 0) 0%, rgba(45, 91, 165, 1) 100%);
  //   opacity: 30%;
  //   position: absolute;
  //   top: 50%;
  //   left: 10%;
  //   transform: translateY(-50%);
  // }
  // &:after {
  //   content: "";
  //   height: 2px;
  //   width: 40%;
  //   background: rgb(45, 91, 165);
  //   background: linear-gradient(90deg, rgba(45, 91, 165, 1) 0%, rgba(45, 91, 165, 0) 100%);
  //   opacity: 30%;
  //   position: absolute;
  //   top: 50%;
  //   right: 10%;
  //   transform: translateY(-50%);
  // }
  h2 {
    color: $primary-color;
    display: inline-block;
    font-size: 24px;
    line-height: 27px;
    font-weight: $font-weight-semibold;
    margin-bottom: 0;
    padding: 0px 15px;
    background-color: $white;
    z-index: 2;
    position: relative;
    @media (max-width: 767px) {
      font-size: 18px;
      line-height: 24px;
    }
  }
}

// ANCHOR Department section style

.department-slider-wrapper {
  .slick-dots {
    // bottom: 30px;
    li {
      button {
        padding: 0;
        &:before {
          content: "";
          width: 10px;
          height: 10px;
          border: $slider-active 1.5px solid;
          border-radius: 100%;
          opacity: 1;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      &.slick-active {
        button {
          &:before {
            content: "";
            width: 26px;
            height: 7px;
            border: $slider-active 1px solid;
            border-radius: 5px;
            opacity: 1 !important;
            background-color: $slider-active;
          }
        }
      }
    }
  }
}

.department-link-item {
  a {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    text-align: center;
    justify-content: center;
    align-items: center;

    &:hover {
      text-decoration: none;
      h3 {
        color: $primary-color;
        transition: all 0.2s ease-in-out;
      }
    }
    &:hover {
      figure {
        background: #0075b9;
        svg {
          fill: #fff;
        }
      }
    }
  }
  figure {
    float: left;
    width: 71px;
    height: 71px;
    margin-bottom: 10px;
    display: block;
    text-align: center;
    border: 1px solid $slider-active-lighten;
    background-color: $light-blue;
    border-radius: 100%;
    text-align: center;
    padding: 10px;
    transition: 0.3s all ease-in-out;
  }
  .department-heading-wrap {
    h3 {
      font-weight: $font-weight-regular;
      font-size: 10px;
      line-height: 14px;
      color: $primary-color;
      width: 70px;
      word-wrap: break-word;
      @media (max-width: 767px) {
        word-break: break-all;
      }
    }
  }
}

.category-container {
  @media (max-width: 767px) {
    margin-bottom: 50px;
  }
}

// ANCHOR Excess CSS scripy

.product-link-item {
  padding: 15px;
  text-decoration: none;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 3px;
  text-align: center;
  margin-bottom: 30px;
  float: left;
  width: 100%;
  background: #fff;

  img {
    height: 70px !important;
  }
  figure {
    float: left;
    width: 100%;
    margin-bottom: 10px;
    display: block;
    text-align: center;
  }
  .heading-wrap {
    float: left;
    width: 100%;
    text-align: center;
    h3 {
      font-size: 17px;
      color: #333;
      line-height: 17px;
      font-weight: 400;
      margin-bottom: 0px;
      text-align: center;
    }
  }
}

/* Offer Section */
.offer-container {
  background: #dfe6f1;
  @media (max-width: 767px) {
    padding: 30px 20px;
    background: #fff;
  }
  .container {
    border-top: 1px solid #cfd7e5;
    padding-top: 50px;
    padding-bottom: 65px;
    @media (max-width: 767px) {
      padding: 30px 30px 12px;
      border-radius: 30px;
      background: url(../images/new-images/mobile-faster.png) no-repeat center;
      background-size: cover;
      width: 100%;
      margin: 0 auto;
      border: none;
    }
  }

  .row {
    .offer-link-item {
      a {
        @media (max-width: 767px) {
          border-bottom: 1px solid rgba(255, 255, 255, 0.7);
          padding-bottom: 15px;
        }
      }

      &:last-child {
        a {
          @media (max-width: 767px) {
            border: none;
            padding-bottom: 0;
          }
        }
      }
    }
  }
}

.offer-link-item {
  a {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    &:hover {
      text-decoration: none;
    }
    @media (max-width: 767px) {
      margin-bottom: 15px;
    }
  }
}
.offer-link-item figure {
  width: 71px;
  height: 71px;
  border-radius: 50%;
  background-color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #b3d6ea;
  margin-right: 10px;
  margin-bottom: 0;
  @media (max-width: 767px) {
    width: 54px;
    height: 54px;
  }
  img {
    @media (max-width: 767px) {
      width: 66%;
      height: 28px;
    }
  }
}
.heading-wrap h2 {
  font-size: 20px;
  color: #3c525d;
  line-height: 27px;
  margin-bottom: 0;
  @media (max-width: 767px) {
    font-size: 16px;
    line-height: 22px;
    color: #fff;
  }
}
.heading-wrap h3 {
  font-size: 13px;
  line-height: 21px;
  color: #3c525d;
  margin: 0;
  @media (max-width: 767px) {
    font-size: 11px;
    line-height: 17px;
    color: #fff;
  }
}

/* category Section */

.subbanner-container {
  padding: 0px 0px;
}

.image_container {
  padding: 15px 15px;
}

.subbanner-container .image-title h2 {
  font-size: 20px;
  color: #333;
  line-height: 36px;
  font-weight: 600;
  margin-bottom: 8px;
  text-transform: uppercase;
}

.subbanner-container .image_container .img-padding {
  padding: 5px 5px;
}

.image-link-item {
  text-decoration: none;
  color: #333;
  border-radius: 1px;
  text-align: center;
  margin-bottom: 0px;
  float: left;
  width: 100%;
  background: #fff;
}

.image-link-item figure {
  float: left;
  width: 100%;
  margin-bottom: 0px;
  display: block;
  text-align: center;
}
.image-link-item figure img {
  border-radius: 10px;
}
.image_container .heading-wrap {
  float: left;
  width: 100%;
  text-align: center;
}
.image_container .heading-wrap h3 {
  font-size: 15px;
  color: #333;
  line-height: 17px;
  font-weight: 400;
  margin-bottom: 0px;
  text-align: center;
}

.sub-product-link-item {
  padding: 15px;
  text-decoration: none;
  color: #333;
  border-radius: 3px;
  text-align: center;
  float: left;
  width: 100%;
  background: #fff;
  margin: 0px 0px 10px 0px;
}

.sub-product-link-item figure {
  float: left;
  width: 100%;
  margin-bottom: 10px;
  display: block;
  text-align: center;
}
.sub-product-link-item .heading-wrap {
  float: left;
  width: 100%;
  text-align: center;
}
.sub-product-link-item .heading-wrap h3 {
  font-size: 17px;
  color: #333;
  line-height: 17px;
  font-weight: 400;
  margin-bottom: 0px;
  text-align: center;
}

.sub-cat-area {
  // background: #f1f1f1;
  // padding: 20px 10px;
  float: left;
  width: 100%;
  margin: 0 0px 30px 0px;
}

.icon-float {
  float: left;
  margin-right: 10px;
}

/* category Section */
.deal-container {
  background: #fff;
  padding: 40px 0 20px;
  &.trend-deal-container {
    padding: 20px 0 20px;
  }
  &--haaland {
    .customer-logos {
      position: relative;
      top: -30px;
    }
  }
  &__haaland {
    background: url(../images/haaland-bg.png) no-repeat 0 0 / cover;
    min-height: 400px;
  }

  &__lg {
    background: url(../images/lg-bg.png) no-repeat 0 0 / cover;
    min-height: 400px;
  }

  &.home-new-product {
    padding: 10px 0 10px;
  }
  &.for-mobile {
    padding: 30px 0;
    @media (max-width: 767px) {
      padding: 26px 0 50px;
      background: #dfe6f1;
    }
    .product-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 25px;
      h2 {
        color: #3c525d;
        font-size: 25px;
        line-height: 27px;
        font-weight: 600;
        margin-bottom: 10px;

        @media (max-width: 767px) {
          font-size: 18px;
          line-height: 24px;
          font-weight: 500;
          color: #000;
          margin: 0;
        }
      }
      // a{
      //   font-size: 13px;
      //   color: #000;
      //   text-decoration: underline;
      // }
    }
    .deal-link-item {
      background: #fff;
      min-height: auto;
      border-radius: 20px;
      padding: 7px 7px 10px;
      figure {
        min-height: 180px;
        border-radius: 15px;
      }
      h2 {
        font-size: 10px;
        line-height: 13px;
        text-align: left;
      }
      .review {
        span {
          font-size: 11px;
        }
      }
      .pricecart {
        .price {
          font-size: 12px;
          line-height: 13px;
          text-align: left;
        }
      }
    }
  }
  .slide {
    padding-left: 5px;
  }
}

.product-title h2 span {
  color: #777777;
  font-size: 18px;
}

/*  .deal-link-item {
        padding: 15px 0px 15px 0px;
    text-decoration: none;
    color: #333;
  text-align:center;
      float: left;
  width:100%;
  border:1px solid #ccc;
    border-radius:3px;
} */

.deal-link-item {
  position: relative;
  padding: 4px 4px 20px;
  text-decoration: none;
  color: #3c525d;
  text-align: center;
  float: left;
  width: 90%;
  border: 1px solid #e3eaee;
  border-radius: 6px;
  &.new-product-card {
    padding: 0 0 10px;
    .heading-wrap {
      padding-bottom: 0;
      min-height: 29px;
      h2 {
        -webkit-line-clamp: 1;
        min-height: 29px;
        margin-bottom: 6px;
        padding-left: 6px;
        @media (max-width: 767px) {
          font-size: 13px;
        }
      }
    }
    figure {
      min-height: 170px;
      padding: 10px;
      margin-bottom: 10px;
      img {
        max-width: 90%;
      }
    }
  }
  &--skeleton {
    width: 100%;
  }
  a {
    text-decoration: none !important;
  }
}

.deal-link-item figure {
  position: relative;
  text-align: center;
  float: left;
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  background-color: #fff;
  min-height: 234px;
  display: flex;
  align-items: center;
  justify-content: center;
  // border-bottom: 1px solid #E3EAEE;
  .wishlist-link {
    position: absolute;
    left: 14px;
    top: 14px;
    z-index: 1;
    background-color: red;
    display: none;
  }
}

.deal-link-item figure img {
  max-width: 100%;
  /*height:200px;*/
}

.deal-link-item .heading-wrap {
  display: flex;
  flex-direction: column;
  min-height: 130px;
  padding: 0 5px 5px;
  width: 100%;
  @media (max-width: 767px) {
    min-height: 95px;
  }
}
.deal-link-item .heading-wrap h2 {
  font-size: 14px;
  color: #3c525d;
  line-height: 27px;
  font-weight: 400;
  margin-bottom: 8px;
  word-wrap: break-word;
  width: 100%;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 54px;
  @media (max-width: 767px) {
    min-height: auto;
  }
}
.deal-link-item .heading-wrap .review img {
  margin-right: 10px;
  width: auto !important;
}
.deal-link-item .heading-wrap .review span {
  font-size: 14px;
  color: #3c525d;
  position: relative;
  top: 2px;
}
.deal-link-item .heading-wrap h3 {
  font-size: 14px;
  line-height: 24px;
  color: #333;
  text-align: center;
  float: left;
  width: 100%;
}

.deal-link-item .heading-wrap .price {
  font-size: 20px;
  color: #3c525d;
  line-height: 24px;
  font-weight: 600;
  margin: 0;
  text-align: left;
  word-wrap: break-word;
  width: 85%;
}
.deal-link-item .heading-wrap button {
  background-color: transparent;
}

.oldprice {
  text-decoration: line-through;
}

.tcb-product-slider {
  padding: 100px 0;
  float: left;
  width: 100%;
}
.tcb-product-slider .carousel-control {
  width: 5%;
}
.tcb-product-item a {
  color: #147196;
}
.tcb-product-item a:hover {
  text-decoration: none;
}
.tcb-product-item .tcb-hline {
  margin: 10px 0;
  height: 1px;
  background: #ccc;
}

// Trending product
.trend-product {
  position: relative;
  max-width: 255px;
  width: 100%;
  background: url(../images/new-images/trend-product.png) no-repeat center
    bottom;
  background-size: cover;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  flex-direction: column;
  min-height: 412px;
  figure {
    margin: 0;
  }
}
.trend-review {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.26);
  padding: 10px 15px;
  max-width: 200px;
  width: 100%;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  img {
    margin-right: 9px;
  }
  span {
    font-size: 16px;
    line-height: 27px;
    font-weight: 500;
    color: #fff;
  }
}
.trend-bottom {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 0 15px;
  h2 {
    color: #fff;
    font-size: 27px;
    line-height: 37px;
    font-weight: 500;
    margin: 0;
  }
  img {
    position: relative;
    top: -10px;
  }
}
.review {
  text-align: left;
  margin-bottom: 10px;
}
.pricecart {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;

  img {
    width: auto !important;
  }
}

.brandshop-container {
  margin-top: 30px;
  text-align: center;
  padding: 0 0 30px;
  @media only screen and (min-width: 1024px) {
    padding: 0 0 50px;
  }
  @media (max-width: 1180px) {
    margin-top: 70px;
  }
  @media (max-width: 767px) {
    margin-top: 20px;
  }
}
.brandshop-container .product-title {
  position: relative;
  text-align: center;
  margin-bottom: 35px;
}
.brandshop-container .bs {
  display: flex;
  align-items: center;
  justify-content: center;
}
.brandshop-container .bs img {
  max-width: 110px;
  height: auto;
  max-height: 29px;
}
.brandwrap {
  padding: 25px 20px;
  border: 1px solid #c0cee4;
  border-radius: 50px;
  .col-lg-3-brand {
    img {
      filter: grayscale(100%);
    }
    a:hover {
      img {
        filter: grayscale(0);
      }
    }
  }
}

.recomended {
  position: relative;
  .recomended-top {
    // padding: 98px 0 178px;
    // background: url(../images/new-images/upto-70.jpg) no-repeat top center;
    // background-size: cover;
    .banner-link {
      display: block;
      img {
        width: 100%;
      }
    }
    .recomended-top-text {
      h2 {
        font-size: 44px;
        line-height: 60px;
        font-weight: 700;
        color: #fff;
        span {
          font-family: auto;
        }
      }
      p {
        font-size: 15px;
        line-height: 23px;
        color: #fff;
        font-weight: 500;
        margin-bottom: 25px;
      }
      a {
        color: #0075b9;
        font-size: 16px;
        border-radius: 64px;
        min-width: 160px;
        width: auto;
        background-color: #fff;
        padding: 13px 15px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        img {
          margin-left: 7px;
        }
      }
    }
    .top-70-text {
      margin-top: 40%;
      span {
        font-size: 47px;
        line-height: 60px;
        color: #fff;
      }
      h3 {
        color: #fff;
        font-size: 354px;
        font-weight: 600;
        line-height: 290px;
        margin: 0;
      }
    }
  }
  .recomended-botttom {
    background-color: #f1f1f1;
    padding: 0 0 50px;
    @media (max-width: 767px) {
      padding: 0 0 75px !important;
    }
    .recomended-botttom-text {
      padding-top: 43px;
      @media (max-width: 767px) {
        display: none;
      }
      h4 {
        font-size: 25px;
        line-height: 27px;
        color: #3c525d;
        font-weight: 600;
        margin: 0 0 10px;
      }
      p {
        font-size: 15px;
        line-height: 23px;
        color: #3c525d;
        font-weight: 500;
      }
    }
  }
  .recomended-botttom-slider {
    margin-top: -20%;
    @media (max-width: 767px) {
      margin-top: 26px;
    }
    .slick-prev {
      display: block !important;
      position: absolute;
      right: -70px;
      left: inherit;
      top: 68%;
      width: 40px !important;
      height: 40px !important;
      background: #0075b9
        url(../images/new-images/icon/home-slide-arrow-prev.svg) no-repeat
        center !important;
      background-size: 16px;
      &.slick-disabled {
        opacity: 0.2;
      }
      &:before {
        display: none;
      }
      @media (max-width: 820px) {
        right: inherit;
        left: -35px;
        top: 50%;
      }
      @media (max-width: 767px) {
        right: inherit;
        top: 112%;
        left: 35%;
      }
    }
    .slick-next {
      display: block !important;
      position: absolute;
      right: -130px;
      left: inherit;
      top: 68%;
      width: 40px !important;
      height: 40px !important;
      background: #0075b9
        url(../images/new-images/icon/home-slide-arrow-next.svg) no-repeat
        center !important;
      background-size: 16px;
      &.slick-disabled {
        opacity: 0.2;
      }
      &:before {
        display: none;
      }
      @media (max-width: 820px) {
        right: -35px;
        left: inherit;
        top: 50%;
      }
      @media (max-width: 767px) {
        top: 112%;
        right: 35%;
      }
    }
  }
  .recomended-slide {
    width: 93% !important;
    border-radius: 30px;
    border: 1px solid #c0cee4;
    background-color: #dfe6f1;
    padding: 4px 4px 20px;
    min-height: 428px;
    @media (max-width: 767px) {
      min-height: auto;
      padding: 7px 7px 10px;
      border-radius: 20px;
    }
    a {
      text-decoration: none !important;
    }
    .figure-area {
      position: relative;
      width: 100%;
      // background-color: #dfe6f1;
      background-color: #fff;
      border-radius: 30px;
      overflow: hidden;
      min-height: 238px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      @media (max-width: 767px) {
        min-height: 180px;
        border-radius: 15px;
      }
      .wishlist-link {
        position: absolute;
        left: 20px;
        top: 10px;
        display: none;
        img {
          width: auto !important;
        }
      }
      figure {
        margin: 0;
      }
    }
    .slide-test-area {
      min-height: 170px;
      display: flex;
      flex-direction: column;
      padding: 0 10px 5px;
      @media (max-width: 767px) {
        min-height: 120px;
      }
      h3 {
        color: #3c525d;
        font-size: 20px;
        line-height: 27px;
        font-weight: 400;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        @media (max-width: 767px) {
          font-size: 11px;
          line-height: 15px;
          text-align: left;
        }
      }
      .review {
        margin-top: 6px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        @media (max-width: 767px) {
          margin-top: 8px;
        }
        img {
          margin-right: 7px;
          width: auto !important;
        }
        span {
          font-size: 14px;
          line-height: 27px;
          color: #3c525d;
          @media (max-width: 767px) {
            font-size: 11px;
            line-height: 14px;
          }
        }
      }
      .pricecart {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        margin-top: auto;
        h5 {
          font-size: 16px;
          line-height: 27px;
          font-weight: 600;
          color: #3c525d;
          margin: 0;
          word-wrap: break-word;
          width: 70%;
          @media (max-width: 767px) {
            font-size: 14px;
            line-height: normal;
            text-align: left;
          }
        }
        a {
          img {
            width: auto !important;
          }
        }
        button {
          background-color: transparent;
        }
      }
    }
  }

  &--offfifty {
    margin-top: 20px;
    @media (max-width: 767px) {
      margin-top: 0;
    }
    .recomended-top {
      // background: url(../images/new-images/upto-50.jpg) no-repeat top center;
      // background-size: cover;
      .recomended-top-text {
        h2 {
          color: #000;
          font-weight: 500;
        }
        p {
          color: #000;
          font-weight: 400;
        }
      }
      .top-70-text {
        margin: 0;
        h3 {
          font-size: 214px;
          line-height: 190px;
          font-weight: 500;
        }
      }
    }
    .recomended-botttom {
      padding: 0 0 145px;
    }
  }
}
.customer-logos .slick-list {
  overflow: hidden;
  margin: 0 0 0 0 !important;
  padding: 0;
  @media (max-width: 767px) {
    margin: 0 0 0 17px !important;
  }
}
.customer-logos .slick-list .slick-slide {
  @media (max-width: 767px) {
    text-align: center;
  }
}

.main-banner {
  @media only screen and (max-width: 767px) {
    padding: 0px 15px;
  }
  .container {
    @media (max-width: 767px) {
      padding: 0;
    }
  }
  .banner-item {
    @media (max-width: 767px) {
      border-radius: 0;
    }
  }
  &--mobile {
    .col-lg-12 {
      @media (max-width: 767px) {
        padding: 0;
      }
    }
    .slick-dots {
      @media (max-width: 767px) {
        display: none !important;
      }
    }
  }
}

.partner-container .product-title h2 {
  color: #3c525d;
  display: inline-block;
  font-size: 24px;
  line-height: 27px;
  font-weight: 600;
  margin-bottom: 0;
  background-color: #fff;
  z-index: 2;
  position: relative;
  padding: 0;
  @media (max-width: 767px) {
    font-size: 18px;
    line-height: 24px;
  }
}

.partner-container {
  .slick-list {
    @media only screen and (min-width: 992px) {
      padding: 21px 30px;
      border-radius: 70px;
      border: 1px solid rgba(45, 91, 165, 0.3);
    }
  }
}

.customer-logos {
  width: 100%;
  .slick-dots {
    margin: 0 0 20px;
    bottom: -40px;
    li {
      position: relative;
      display: inline-block;
      width: 20px;
      height: 20px;
      margin: 0 5px;
      padding: 0;
      cursor: pointer;
      button {
        &:before {
          content: "";
          width: 10px;
          height: 10px;
          border: #0075b9 1.5px solid;
          border-radius: 100%;
          opacity: 1;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      &.slick-active {
        button {
          &:before {
            content: "";
            width: 26px;
            height: 7px;
            border: #0075b9 1px solid;
            border-radius: 5px;
            opacity: 1 !important;
            background-color: #0075b9;
          }
        }
      }
    }
  }
}

.hoverded-card {
  &:hover {
    text-decoration: none;
  }
}

.sticky-badge {
  position: relative;
  display: inline-block;
  background-color: #de1d1d;
  color: white;
  font-weight: bold;
  font-size: 10px;
  text-transform: uppercase;
  border-radius: 0 4px 4px 4px;
  position: absolute;
  top: 15px;
  left: -6px;
  height: 15px;
  width: 43px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sticky-badge::after {
  content: "";
  position: absolute;
  top: -6px;
  left: 0;
  width: 0;
  height: 0;
  border-top: 6px solid #991212;
  border-right: 6px solid transparent;
  transform: rotate(180deg);
}

.modal-dialog.modal-md {
  padding: 70px 0;
}

.app-container {
  padding: 0px 10px;
  width: auto;
  border-radius: 0px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  overflow: hidden;
  //height: 100vh;
}
.exercise-card {
  padding: 50px 0 20px;
  text-align: center;
  background: #fff;
  border-radius: 0px;
}

.exercise-card .header {
  margin-bottom: 20px;
}
.exercise-card .header img {
  margin-bottom: 15px;
}
.exercise-card .header h2 {
  font-size: 20px;
  margin: 0;
}

.header p {
  color: #47545b;
  font-size: 14px;
}

.exercise-image {
  margin: 15px 0px;
  padding: 0 10px;
}
.slick-track {
  // display: flex !important;
  // align-items: center !important;
}

.exercise-image h3 {
  color: #3c9ed7;
}
.exercise-image img {
  width: 100%;
  border-radius: 15px;
  border: 1px solid #de1d1d;
  padding: 4px;
  box-sizing: border-box;
  max-height: 100% !important;
}
.app-container .slick-slide:nth-child(1) img {
  border: 2px solid #e5b40f;
}
.app-container .slick-slide:nth-child(2) img {
  border: 2px solid #0b8f75;
}
.app-container .slick-slide:nth-child(3) img {
  border: 2px solid #f5ec00;
}
.app-container .slick-slide:nth-child(4) img {
  border: 2px solid #ff363f !important;
}
.app-container .slick-slide:nth-child(5) img {
  border: 2px solid #3be500;
}
.app-container .slick-slide:nth-child(6) img {
  border: 2px solid #77af04;
}
.app-container .slick-slide:nth-child(7) img {
  border: 2px solid #e500d8;
}
.app-container .slick-slide:nth-child(8) img {
  border: 2px solid #de1d1d;
}
.exercise-image.slick-slide.slick-current.slick-active.slick-center {
  zoom: 110%;
  animation: mymove 5s infinite;
}
.exercise-image.slick-slide.slick-current.slick-active.slick-center img {
  border: 2px solid #3c9ed7;
}
.exercise-details {
  margin-top: 30px;
}
.exercise-details h3 {
  font-size: 22px;
  color: #007bff;
  margin: 10px 0 5px;
}

.exercise-details p {
  font-size: 14px;
  color: #47545b;
  margin: 0 0 15px;
}

.difficulty-level {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin: 30px 0;
}
.difficulty-level3 a {
  margin-top: 5px;
  display: block;
}
.difficulty-level3 img {
  border-radius: 9px;
}
.level-btn {
  padding: 10px 25px;
  border: 1px solid #3c9ed7;
  border-radius: 20px;
  background-color: #f9f9f9;
  cursor: pointer;
  font-size: 14px;
  color: #222;
}

.level-btn.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.add-exercise-btn {
  display: block;
  width: 100%;
  padding: 15px;
  background-color: #ff6c37;
  color: #fff;
  font-size: 16px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  margin-top: 15px;
}

.add-exercise-btn:hover {
  background-color: #e65a30;
}

.trend-product-section {
  $trend-product-section: &;
  padding: 60px 0;
  .mb-30 {
    margin-bottom: 15px;
  }
  &__wishlist {
    position: absolute;
    top: 15px;
    left: 15px;
    img {
      width: auto;
      height: auto;
    }
  }
  &__box {
    padding: 4px 4px 25px 4px;
    border-radius: 10px;
    border: 1px solid rgba(45, 91, 165, 0.3);
    display: inline-block;
    &:hover {
      text-decoration: none;
    }
  }
  &__box-img {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    position: relative;
    padding: 30px;
    img {
      width: 80%;
      height: 80%;
      object-fit: contain;
    }
  }
  h3 {
    font-size: 14px;
    line-height: 19px;
    color: #3c525d;
    padding-right: 11px;
    padding-left: 11px;
    margin-top: 20px;
  }
  &__box-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 11px 0 11px;
    margin-top: 20px;
    h4 {
      font-size: 20px;
      line-height: 27px;
      color: #3c525d;
      font-weight: 500;
      margin-bottom: 0;
    }
  }
}

.product-banner-area {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 20px;
  padding-top: 50px;
  &--top-spacing {
    padding-top: 30px;
    margin-bottom: 100px;
  }
  &__box {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 310px;
    img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  &__box1 {
    flex: 0 0 48%;
    max-width: 48%;
    h3 {
      position: absolute;
      font-size: 40px;
      font-weight: 700;
      color: #a42c6a;
      left: 30px;
      top: 17%;
      max-width: 250px;
    }
    p {
      position: absolute;
      font-size: 14px;
      font-weight: 500;
      color: #a42c6a;
      left: 30px;
      top: 64%;
      max-width: 255px;
    }
  }
  &__box2 {
    flex: 1;
    h3 {
      position: absolute;
      font-size: 40px;
      font-weight: 700;
      color: #fff;
      left: 30px;
      right: 30px;
      margin: 0 auto;
      text-align: center;
      top: 9%;
      max-width: 250px;
    }
  }
  &__box3 {
    flex: 1;
    h3 {
      position: absolute;
      font-size: 40px;
      font-weight: 700;
      color: #a42c6a;
      left: 30px;
      right: 30px;
      margin: 0 auto;
      text-align: center;
      bottom: 4%;
      max-width: 250px;
    }
  }
  &__box4 {
    flex: 1;
    h3 {
      position: absolute;
      font-size: 40px;
      font-weight: 700;
      color: #fff;
      left: 30px;
      right: 30px;
      margin: 0 auto;
      text-align: center;
      top: 6%;
      max-width: 250px;
    }
  }
  &__box5 {
    flex: 0 0 48%;
    h3 {
      position: absolute;
      font-size: 40px;
      font-weight: 700;
      color: #fff;
      left: 30px;
      top: 24%;
      max-width: 250px;
    }
    p {
      position: absolute;
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      opacity: 0.3;
      left: 30px;
      top: 60%;
      max-width: 255px;
    }
  }
  &__box6 {
    flex: 1;
    h3 {
      position: absolute;
      font-size: 40px;
      font-weight: 700;
      color: #fff;
      left: 30px;
      right: 30px;
      margin: 0 auto;
      text-align: center;
      top: 6%;
      max-width: 250px;
    }
  }
}

.video-slide-section {
  $video-slide-section: &;
  padding: 60px 0;
  h2 {
    font-size: 24px;
    line-height: 27px;
    font-weight: 600;
    color: #3c525d;
  }
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(71, 84, 91, 0.2);
    padding-bottom: 15px;
    margin-bottom: 30px;
  }
  &__item {
    &:hover {
      text-decoration: none;
    }
  }
  &__img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid rgba(45, 91, 165, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 70px !important;
      height: auto;
      object-fit: cover;
    }
  }
  h3 {
    font-size: 14px;
    line-height: 19px;
    font-weight: 400;
    color: #3c525d;
    width: 135px;
    margin-top: 20px;
  }
  &--container-smart-watch {
    margin-top: 60px;
  }
}

.testimonial-section {
  background-color: #F2F6F8;
  padding: 60px 20px 100px;
  text-align: center;
  .testimonial-heading {
    color: #2e3a59;
    font-size: 28px;
    margin-bottom: 40px;
  }
  .testimonial-slider-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .testimonial-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    max-width: 100%;
    margin: auto;
    text-align: left;
  }

  .quote-icon {
    width: 34px;
    margin-bottom: 20px;
  }

  .testimonial-title {
    font-weight: 600;
    font-size: 12px;
    line-height: 19px;
    color: #313641;
    opacity: 0.8;
  }

  .testimonial-description {
    color: #313641;
    font-size: 10px;
    line-height: 14px;
    margin: 15px 0;
    opacity: 0.8;
  }

  .testimonial-user {
    display: flex;
    align-items: center;
    margin-top: 30px;
    position: relative;
    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: -15px;
      width: 39px;
      height: 1px;
      background-color: #de1d1d;
    }
  }

  .testimonial-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
  }

  .testimonial-name {
    font-size: 14px;
    font-weight: 600;
    color: #313641;
  }

  .testimonial-role {
    font-size: 10px;
    color: #313641;
  }
  .slick-prev,
  .slick-next {
    z-index: 10;
    display: flex !important;
    align-items: center;
    justify-content: center;
    border: 1px solid #2e3a59;
    background: none;
        &:hover {
          &:before {
            background: #0075B9 url(../images/right-arrow.png) no-repeat 7px 5px;
          }
        }
  }

  .slick-prev:before,
  .slick-next:before {
    color: #2e3a59;
    font-size: 20px;
    opacity: 1;
  }

  .slick-prev {
    left: 46.5%;
    bottom: 0;
    top: inherit;
    background: transparent;
    &:before {
      content: '';
      border: 1px solid #0075B9;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      background: url(../images/left.png) no-repeat 7px 5px;
    }
    &:hover {
      &:before {
        background: #0075B9 url(../images/right-arrow.png) no-repeat 9px 5px;
        transform: rotate(180deg);
      }
    }
  }

  .slick-next {
    right: 46.5%;
    bottom: 0;
    top: inherit;
    &:before {
      content: '';
      border: 1px solid #0075B9;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      background: url(../images/left.png) no-repeat 7px 5px;
      transform: rotate(180deg);
    }
    &:hover {
      &:before {
        background: #0075B9 url(../images/right-arrow.png) no-repeat 9px 5px;
        transform: rotate(0deg);
      }
    }
  }

  .slick-arrow {
    bottom: -85px;
  }
  .testimonial-slider-container .slick-slide {
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0.7;
  transform: scale(0.9);
}

.testimonial-slider-container .slick-center {
  transform: scale(1);
  opacity: 1;
  z-index: 2;
}
}

.feature-product {
  padding-bottom: 100px;
  .heading-wrap {
    min-height: auto;
    padding-bottom: 0;
    h2 {
      max-width: 170px;
      line-height: 19px;
      min-height: auto
    }
  }
  .pricecart {
    margin: 0;
  }
  .product-title {
    h2 {
      padding: 0
    };
  }
}


.deal-container--with-color-img {
  .deal-link-item {
    background: #F2F6F8;
    figure {
      border-bottom: 1px solid #E3EAEE;
    }
  }
}

.wraper--overflow-default {
  overflow: visible;
}