    /*
    Flaticon icon font: Flaticon
    Creation date: 21/10/2016 08:38
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-arrows:before { content: "\f100"; }
.flaticon-commerce:before { content: "\f101"; }
.flaticon-graphic:before { content: "\f102"; }
.flaticon-graphic-1:before { content: "\f103"; }
.flaticon-handshake:before { content: "\f104"; }
.flaticon-headphones:before { content: "\f105"; }
.flaticon-interface:before { content: "\f106"; }
.flaticon-layers:before { content: "\f107"; }
.flaticon-light-bulb:before { content: "\f108"; }
.flaticon-mail:before { content: "\f109"; }
.flaticon-share:before { content: "\f10a"; }
.flaticon-target:before { content: "\f10b"; }
.flaticon-text-on-paper-sheet-sketch:before { content: "\f10c"; }
    
    $font-Flaticon-arrows: "\f100";
    $font-Flaticon-commerce: "\f101";
    $font-Flaticon-graphic: "\f102";
    $font-Flaticon-graphic-1: "\f103";
    $font-Flaticon-handshake: "\f104";
    $font-Flaticon-headphones: "\f105";
    $font-Flaticon-interface: "\f106";
    $font-Flaticon-layers: "\f107";
    $font-Flaticon-light-bulb: "\f108";
    $font-Flaticon-mail: "\f109";
    $font-Flaticon-share: "\f10a";
    $font-Flaticon-target: "\f10b";
    $font-Flaticon-text-on-paper-sheet-sketch: "\f10c";