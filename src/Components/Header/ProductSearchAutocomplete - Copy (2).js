import React from 'react';
import { Link } from "react-router-dom";
import { redirectTo } from './redirecturl';
//import axios from 'axios';

class ProductSearchAutocomplete extends React.Component {
    token = null;
    constructor(props){
        super(props)
        this.state = {
            query: '',
            suggestions: [],
            text: '',
            searchingData: false,
            setShowSuggestions: false,
            clickedSearchData: [],
            clickedSearchTextBox: false,
            clickCountInSearchBox: 0,
            isSearchProductLoaded: false
        }

        this.handleOnSearchSubmit = this.handleOnSearchSubmit.bind(this);
        this.inputRef = React.createRef();
    }

    handleOuterClick = e => {
        if (!this.inputRef.current.contains(e.target)) {
          this.setState({setShowSuggestions: false, clickedSearchTextBox: false});
        }
    };

    componentDidMount() {
        document.addEventListener("click", this.handleOuterClick);
    }

    handleSearchFormValidation(){
        let searchtxt = this.state.text;
        let searchFormIsValid = true;

        if(!searchtxt){
            searchFormIsValid = false;
        }

        return searchFormIsValid;
    }

    handleOnSearchSubmit(e){
        e.preventDefault();

        if(this.handleSearchFormValidation()){
            let searchtxt = this.state.text;
            return redirectTo("/search/"+searchtxt);
        }
    }

    handleOnProductRedirect(productId, productName, productPrice, productImage){
        const prodObj = {model: productId, name: productName, price: productPrice, image: productImage};

        if (this.state.clickedSearchData.filter(searchArr => searchArr.model === prodObj.model).length === 0) {
            this.setState({ clickedSearchData: [...this.state.clickedSearchData, prodObj] }, function() {
                localStorage.setItem("searchData", JSON.stringify(this.state.clickedSearchData));
                console.log(this.state.clickedSearchData);
                //console.log(prodObj);
            });
        }
        
        //return redirectTo("/producto/"+productId);
    }

    getInfo = async function () {
        this.setState({searchingData: true})
        let userSearchText = this.state.query;
        const apiUrl = 'https://www.evisionstore.com/api/home/<USER>';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({"keyword": userSearchText}),
            //mode: 'cors'
        };

        const token = {};
        this.token = token;

        await fetch(apiUrl, requestOptions)
        .then(res => res.json())
        .then(
            (result) => {
                //console.log(result)
                if (this.token === token) {
                    if(result.status===200){
                        this.setState({
                            suggestions: result.search_result,
                            //text: userSearchText,
                            searchingData: false,
                            isSearchProductLoaded: true
                        })
                    } else {
                        this.setState({
                            suggestions: [],
                            searchingData: false,
                            isSearchProductLoaded: true
                        })
                    }
                }
            },
            (error) => {
                this.setState({ error: error });
            }
        )
        // axios.get(`https://evisionstore.com/api/home/<USER>
        //     .then(({ data }) => {
        //         this.setState({
        //             suggestions: data.search_result,
        //             text: this.state.query,
        //             searchingData: false
        //         })
        //     })
    }

    onTextClick = () => {
        this.setState({ setShowSuggestions: true });
    }

    onTextChange = (e) => {
        if(e.target.value !== "") {
            this.setState({
                query: e.target.value,
                text: e.target.value,
                suggestions: [],
                setShowSuggestions: true,
                clickCountInSearchBox: 1
            }, () => {
                if (this.state.query.length !== 0) {
                    //if (this.state.query.length % 2 === 0) {
                        this.getInfo()
                        //let userSearchText = this.state.query;
                        //this.props.searchProductByKeywords(userSearchText);
                    //}
                }
            })
        } else {
            this.setState({
                text: e.target.value,
                suggestions: [],
                setShowSuggestions: false
            });
        }
    }

    selectedText(value) {
        this.setState(() => ({
            text: value,
            suggestions: [],
        }))
    }

    renderSuggestions = (isSetShowSuggestions) => {
        let { suggestions, isSearchProductLoaded } = this.state;
        const hidden = isSetShowSuggestions ? '' : 'hidden'; 

        if(isSearchProductLoaded){
            return (
                <ul className={`autocomplete ${hidden}`}>
                    {suggestions.length !== 0 ? (
                        suggestions.map(suggestions => (
                            // <li key={suggestions.product_id} onClick={() => this.selectedText(suggestions.product_title)}>
                            <li key={suggestions.product_id}>
                                <Link to="#" onClick={() => this.handleOnProductRedirect(suggestions.product_id, suggestions.product_title, suggestions.product_price, suggestions.product_image)}>
                                    <div className="img-sec">
                                        <img className="prod-img" src={suggestions.product_image} alt={suggestions.product_title} />
                                    </div>
                                    <div className="desc-sec">
                                        <p dangerouslySetInnerHTML={{__html: suggestions.product_title }} />
                                        {(() => {
                                            if (suggestions.product_price === '0.00'){
                                                return (<></>)
                                            } else {
                                                return (
                                                    <p>
                                                        <span className="price-span">Precio:</span> ${suggestions.product_price}

                                                        {suggestions.product_regular_price ? (
                                                            <span className="reg-price-span">
                                                                ${suggestions.product_regular_price}
                                                            </span>
                                                        ) : (
                                                            <></>
                                                        )}
                                                    </p>
                                                )
                                            }
                                        })()}
                                        {(() => {
                                            if(suggestions.allow_purchase === 1){
                                                if (suggestions.product_available_quantity > 0){
                                                    return (<p className="instock-sec">En Stock</p>)
                                                } else {
                                                    return (
                                                        <p className="outstock-sec">Fuera de stock</p>
                                                    )
                                                }
                                            }else{
                                                return (
                                                    <p className="outstock-sec">Fuera de stock</p>
                                                )
                                            }
                                        })()}
                                        {/* <p>{suggestions.product_description}</p> */}
                                    </div>
                                    <div className="sep-sec" />
                                </Link>
                            </li>
                        ))
                    ) : (
                        <li>
                            <p>No se han encontrado resultados!</p>
                        </li>
                    )
                }
                </ul>
            );
            
        } else {
            let searchData = JSON.parse(localStorage.getItem("searchData"));
            let isClickCountInSearchBox = this.state.clickCountInSearchBox;
            
            if(isClickCountInSearchBox === 0 && searchData !== null){
                return (
                    <ul className={`autocomplete ${hidden}`}>
                        {Object.values(searchData).length !== 0 ? (
                            Object.values(searchData).map((search, index) => (
                                <li key={index}>
                                    <Link to="#" onClick={() => this.handleOnProductRedirect(search.model, search.name, search.price, search.image)}>
                                        <div className="img-sec">
                                            <img className="prod-img" src={search.image} alt={search.name} />
                                        </div>
                                        <div className="desc-sec">
                                            <p>{search.name}</p>
                                            <p><span className="price-span">Precio:</span> ${search.price}</p>
                                        </div>
                                        <div className="sep-sec" />
                                    </Link>
                                </li>
                            ))
                        ) : (
                            <></>
                        )
                    }
                    </ul>
                );
            }
        }
    }

    render() {
        const { text } = this.state;
        
        return (
            <>
                <div className="search_box_area"  ref={this.inputRef}>
                    <form onSubmit={this.handleOnSearchSubmit}>
                        <input type="text" placeholder="búsqueda" name="search" onChange={this.onTextChange} value={text} autoComplete="off" onClick={this.onTextClick} />
                        <button type="submit">
                            {this.state.searchingData ? <i className="fa fa-refresh fa-spin" /> : <i className="fa fa-search" />}
                        </button>
                    </form>
                </div>
                {(() => {
                    
                        return (
                            <>
                            {this.renderSuggestions(this.state.setShowSuggestions)}
                            </>
                        )
                    
                })()}
            </>
        )
    }
}

export default ProductSearchAutocomplete;