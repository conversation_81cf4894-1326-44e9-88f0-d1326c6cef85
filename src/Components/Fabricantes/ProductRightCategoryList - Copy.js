import React, { useState, useEffect } from 'react';
import { useParams } from "react-router";
import { Link } from "react-router-dom";

function ProductRightCategoryList(props) {
    const [isProductsLoaded, setProductsLoaded] = useState(false);
    const [isProductError, setProductError] = useState("");
    const [categoryProducts, setCategoryProducts] = useState([]);

    let { categoryId } = useParams();
    
    
    //console.log(categoryId);

    useEffect(() => {
        setProductsLoaded(false);
        //setCategoryProducts([]);
        const apiUrl = 'https://www.evisionstore.com/api/product/list.php';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({"category_id": categoryId})
        };

        fetch(apiUrl, requestOptions)
        .then(res => res.json())
        .then(
            (result) => {
                setCategoryProducts(result.product_list);
                setProductsLoaded(true);
            },
            (error) => {
                setProductError(error);
            }
        )
    }, [categoryId]);

    function addToCart(){
        
    }

    //console.log(categoryProducts);
    
    if (categoryProducts && categoryProducts.length > 0) {

        return(
            <div className="col-lg-9 col-md-9 col-sm-12 col-xs-12 catagory-container-right">
                <div className="row">
                {categoryProducts.map((localState, index) => (
                    <div className="col-lg-4 col-md-4 col-sm-6 col-xs-12" key={localState.product_id}>
                        <div className="deal-link-item">
                            <Link to={`/producto/${localState.product_id}`}>
                                <figure>
                                    <img src={localState.product_image} alt="" />
                                </figure>
                                <div className="heading-wrap">
                                    <div className="price">$ 559.95</div>
                                    <h2>{localState.product_name}</h2>
                                    <h3>{localState.short_description}</h3>
                                    <button onClick={addToCart(localState.product_id)}><i className="fa fa-shopping-cart" aria-hidden="true"></i> Agregar al carrito</button>
                                </div>
                            </Link>
                        </div>	
                    </div>
                    ))}
                </div>
            </div>
        )

    }else{

        return(
            <div className="col-lg-9 col-md-9 col-sm-12 col-xs-12 catagory-container-right">
                <div className="row">
                    <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12" >	
                    <h3>No Product Found.</h3>
                    </div>
                </div>
            </div>
        )

    }
    
}

export default ProductRightCategoryList;