// Variables for reusability
$primary-color: #19255B;
$accent-color: #ee000a;
$font-family: "Poppins", sans-serif;
$border-radius: 30px;

.fingertips_area {
  text-align: center;
  width: 100%;
  margin-top: 40px;

  h2, h3 {
    text-align: center;
    margin: 1.5rem 0 2rem;
    font-size: 2.5rem;
    font-family: $font-family;
    letter-spacing: 0.2px;
    color: $primary-color;
    font-weight: 400;

    span {
      font-size: 3rem;
      color: $accent-color;
      font-weight: 600;
    }
  }

  h3 {
    display: flex;
    justify-content: center;

    a {
      background: $accent-color;
      color: #fff;
      padding: 0 40px;
      display: inline-block;
      text-decoration: none;
      border-radius: $border-radius;
      height: 50px;
      line-height: 50px;
      font-size: 2rem;
      margin: 0 20px;

      &:hover {
        background: $primary-color;
      }
    }
  }
}

.fingertips-cat-area {
  &::before, &::after {
    content: '';
    display: block;
    clear: both;
  }

  .similar-catagory {
    position: relative;
    width: 100%;
    height: 180px;
    border-radius: $border-radius;
    overflow: hidden;
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    padding: 10px 15px;

    h4 {
      position: relative;
      font-size: 22px;
      line-height: 30px;
      color: #fff;
      margin-bottom: 10px;
      width: 100%;
      text-transform: capitalize;
      text-align: left;
    }

    img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      left: 0;
      top: 0;
    }

    .category-similer-box-link {
      position: relative;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      text-decoration: none !important;

      img {
        position: relative;
        width: auto;
        height: auto;
        margin-left: 7px;
        object-fit: cover;
      }
    }
  }
}

.easy_instant_area, .easy_instant_area2nd {
  text-align: left;
  width: 100%;
  margin-top: 40px;

  img {
    border-radius: $border-radius;
  }

  h2 {
    color: $primary-color;
    line-height: 1;
    font-size: 36px;

    span {
      font-size: 36px;
      color: #e70f47;
      font-weight: 600;
    }
  }

  ul, ol {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}

.easy_instant_area ul li {
  font-family: $font-family;
  font-size: 18px;
  display: flex;
  padding: 10px 0 4px 45px;
  background: url(../images/krediya/bulleticon.png) no-repeat 0 8px;
}

.easy_instant_area2nd ol {
  margin: 30px 0 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  counter-reset: item;

  li {
    font-family: $font-family;
    font-size: 18px;
    display: flex;
    padding: 15px 30px 15px 15px;
    background: $primary-color;
    border-radius: $border-radius;
    margin: 0 10px 20px;
    counter-increment: item;
    color: #fff;

    &:before {
      margin-right: 10px;
      content: counter(item);
      color: white;
      width: 35px;
      text-align: center;
      display: inline-block;
      border-right: 1px solid #fff;
    }
  }
}

.creditbannerleftarea {
  margin-bottom: 20px;
  text-align: left;
  max-width: 40%;

  h1 {
    font-size: 55px;
    font-weight: bold;
    color: #fff;
  }

  p {
    font-size: 30px;
    line-height: 35px;
    margin: 10px 0 40px;
    color: #fff;
  }
}

.krediya-logo {
  margin: 20px 0;

  img {
    max-width: 150px;
  }
}

.creditbannerrightareaimage {
  max-width: 60%;

  img {
    max-width: 100%;
    height: auto;
  }
}

.steps {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 40px 90px;
  background: url(../images/krediya/creditredBg.png) no-repeat center 50%;
  max-width: 1200px;
  margin: 80px auto 0;
  padding: 0 120px 0 160px;
}

.step {
  background-color: #fff;
  color: #000;
  border-radius: 8px;
  padding: 6px 20px 20px 70px;
  width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  text-align: left;
  position: relative;
  min-height: 135px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  img {
    max-width: 102px;
    position: absolute;
    left: -50px;
    top: 20px;
  }

  h3 {
    font-size: 18px;
    margin: 10px 0 0;
  }

  p {
    font-size: 16px;
    margin: 0 0 10px;
  }
}

.creditcontainer {
  margin: 0 auto 270px;
  text-align: center;
  background: #005cf7;
  height: 920px;
}

.creditbannerarea {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 1040px) {
  .creditbannerarea {
    padding: 30px;
    display: block;
  }

  .creditcontainer {
    height: auto;
    margin: 0;
  }

  .steps {
    margin: 0;
    padding: 50px 60px 50px 70px;
    background: #de1d1d;
  }

  .creditbannerleftarea h1 {
    font-size: 44px;
  }
}

@media (max-width: 991px) {
  .creditbannerleftarea h1 {
    font-size: 32px;
  }

  .creditbannerleftarea p {
    font-size: 22px;
  }

  .steps {
    gap: 40px 60px;
    padding: 50px 20px 50px 55px;
  }

  .step {
    width: 310px;
  }

  .step h3 {
    font-size: 16px;
  }

  .step p {
    font-size: 12px;
  }
}

@media (max-width: 767px) {
  .creditbannerleftarea {
    max-width: 100%;
    order: 2;
    margin-top: 40px;
  }

  .creditbannerrightareaimage {
    max-width: 100%;
    order: 1;
  }

  .fingertips_area h2, .fingertips_area h3 {
    font-size: 30px;
  }

  .fingertips_area h3 {
    display: block;

    a {
      margin: 20px;
    }
  }
}
