import React, { useState } from "react";
import { Link, Redirect } from "react-router-dom";
//import {PostData} from '../../Services/PostData';
import { connect } from 'react-redux';
import { logIn } from '../../Services/Actions/user.actions';

function LoginPage(props) {
    let referer;

    try{
        referer = props.location.state.referer;
    } catch (error) {
        referer = "/profile";
    }
    console.log(props.isLoggedIn);
    const isLoggedIn = props.isLoggedIn;
    const isError = false;
    const isOthersError = "";

    const [userName, setUserName] = useState("");
    const [password, setPassword] = useState("");
    const [isUsernameError, setUsernameError] = useState("");
    const [isPasswordError, setPasswordError] = useState("");
    
    function postLogin() {
        let isUsernameValidate = 0;
        let isPasswordValidate = 0;

        if(!userName){
            setUsernameError("Email can not be empty");
        } else {
            let lastAtPos = userName.lastIndexOf('@');
            let lastDotPos = userName.lastIndexOf('.');

            if (!(lastAtPos < lastDotPos && lastAtPos > 0 && userName.indexOf('@@') === -1 && lastDotPos > 2 && (userName.length - lastDotPos) > 2)) {
                setUsernameError("Email is not valid");
            }else{
                isUsernameValidate = 1;
                setUsernameError("");
            }
        } 

        if(!password){
            setPasswordError("Password can not be empty");
        }else{
            isPasswordValidate = 1;
            setPasswordError("");
        }

        if (isUsernameValidate === 1 && isPasswordValidate === 1) {
            var postData = {
                email: userName,
                password: password
            };

            var exactFilenameWithDir = "user/login-for-ractjs.php";
            var postTrough = "fetch";

            props.logIn(postTrough, exactFilenameWithDir, postData);
        }
    }

    if (isLoggedIn) {
        return <Redirect to={referer} />;
    }

    return (
        <div className="container" style={{marginTop: "15px"}}>
            <h3>Log In</h3>
            <p style={{color: "red"}}>{isError ? isOthersError : ''}</p>
            <form >
                <div className="form-group">
                    <label>Email:</label>
                    <input type="username" className="form-control" placeholder="Enter Email"  value={userName} onChange={e => { setUserName(e.target.value); }} />
                    <span style={{color: "red"}}>{isUsernameError}</span>
                </div>
                <div className="form-group">
                    <label>Password:</label>
                    <input type="password" className="form-control" placeholder="Enter Password" value={password} onChange={e => { setPassword(e.target.value); }} />
                    <span style={{color: "red"}}>{isPasswordError}</span>
                </div>
                <div className="form-group">
                    <button type="button" className="btn btn-success" onClick={postLogin}>Submit</button>
                </div>
            </form>
            <Link to="/signup">Don't have an account?</Link>
        </div> 
    );
}

// const mapStateToProps = state => ({
// })

// const MapDispachToProps = dispatch => ({
//     logIn: () => dispatch(logIn())
// })

function mapState(state) {
    const { isLoggedIn } = state.UserReducer;
    return { isLoggedIn };
}

const MapDispachToProps = dispatch => ({
    logIn: () => dispatch(logIn())
})

const LoginComp = connect(
    mapState,
    MapDispachToProps
)(LoginPage)

export default LoginComp;