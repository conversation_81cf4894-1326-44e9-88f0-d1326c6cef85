.cart-item-all-details {
  display: flex;
  align-items: center;
  .cart-rating {
    display: flex;
    align-items: center;
    margin-top: 5px;
    ul {
      display: flex;
      align-items: center;
      li {
        img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
  .cart-thumb_image {
    width: 31%;
    height: 130px;
    display: flex;
    align-items: center;
  }
  .cart-product-review {
    h5 {
      font-size: 12px;
      font-weight: 500;
      color: #131921;
      margin-bottom: 0;
      margin-left: 5px;
      opacity: 0.5;
      position: relative;
      top: 2px;
    }
  }
}

.userInfo--mobile {
  .CartDescription {
    h4 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 3px;
      line-height: 22px;
      a {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 190px;
        display: inline-block;
      }
    }
    p {
      font-size: 14px;
      line-height: 16px;
      color: #7d899d;
    }
  }
  .modprice {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }
  .cart-product_info {
    border-bottom: 0;
  }
  .cart-product_action {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: 100%;
    margin-top: 15px;
    .bootstrap-touchspin {
      flex: 0 0 120px;
      border: 1px solid #c1cdd9;
      border-radius: 6px;
      margin-right: 15px;
    }
    .input-group-btn {
      width: 36px !important;
      height: 38px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #e4ecf4;
      overflow: hidden;
      .btn {
        color: #7d899d;
        font-size: 24px;
      }
    }
    .quanitySniper {
      text-align: center;
      height: 38px !important;
      border: none;
      border-left: 1px solid #c1cdd9;
      border-right: 1px solid #c1cdd9;
    }
    .cart-save-btn {
      height: 40px;
      button {
        font-size: 14px;
        color: #7d899d;
        background: #fff;
        height: 100%;
        border-radius: 6px;
        border: 1px solid #c1cdd9 !important;
        box-shadow: inset 0 0 5px 0 #dbe6f1;
        img {
          margin-right: 5px;
        }
      }
    }
    .action-area {
      font-size: 14px;
      color: #eb5757;
      background: #fff4f4;
      border: 1px solid #eb5757 !important;
      height: 40px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 7px;
      img {
        width: 16px;
        margin-right: 3px;
      }
    }
  }
  .cart-list {
    padding: 0 0;
    border-bottom: 5px solid #ecf2f8;
  }
  .cartFooter {
    padding: 0 15px;
    .btn {
      border-radius: 8px;
    }
  }
  .cart-item {
    margin-bottom: 15px;
    padding-top: 15px;
  }
}
.dashboard-container--mobile {
  padding-top: 0;

  .rightSidebar--mobile {
    padding: 0;
  }
  .recipt-box {
    border-radius: 0;
    padding: 0;
    h4 {
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      text-align: left;
      border-bottom: 1px solid #dfe7f4;
      padding: 15px 15px;
      margin-bottom: 10px;
    }
    .recipt-row {
      padding: 0 15px;
      margin-bottom: 10px;
      &:before {
        display: none;
      }
      span {
        font-size: 14px;
        color: #131921;
      }
      .strong {
        font-size: 14px;
        font-weight: 400;
        color: #131921;
      }
    }
    .recipt-row-last {
      padding-top: 10px;
      border-top: 1px solid #dfe7f4;
      span {
        font-size: 16px !important;
        font-weight: 500 !important;
      }
      .strong {
        font-size: 16px;
        font-weight: 500;
        color: #0075b9;
      }
    }
    .btn-area {
      padding: 0 15px;
      .btn {
        border-radius: 8px;
      }
    }
  }
}

.bottom-sticky-footer-top {
  @media (max-width: 767px) {
    z-index: 9;
  }
}

.dashboard-container--mobile {
  padding-bottom: 25px;
  .progressbar {
    background: #0075b9;
    padding: 10px 0;
    display: flex;
    width: 100%;
    @media (max-width: 767px) {
      padding: 11px 15px;
      counter-reset: section;
      margin: 0 !important;
    }
    li {
      text-align: left;
      position: relative;
      float: none !important;
      width: inherit !important;
      @media (max-width: 767px) {
        margin-right: 5px;
      }
      &:before {
        margin: 0 !important;
        @media (max-width: 767px) {
          width: 18px !important;
          height: 18px !important;
          counter-increment: section;
          content: counter(section) !important;
          line-height: normal !important;
          font-size: 10px;
          font-weight: 600;
          text-align: center;
          border: none !important;
          outline: none !important;
        }
      }
      &:after {
        width: 100% !important;
        @media (max-width: 767px) {
          top: 50% !important;
          transform: translateY(-50%);
        }
      }
      strong {
        color: #fff;
        font-size: 13px;
        position: absolute;
        top: 1px;
        left: 24px;
        background: #0075b9;
        padding: 5px 10px;

        @media (max-width: 767px) {
          top: 0;
          left: 18px;
          padding: 0 5px;
          width: calc(100% - 18px);
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
      &.active {
        &:before {
          background: #ff3131 !important;
          outline: #ff3131 !important;
          border: none !important;
          @media (max-width: 767px) {
            color: #fff;
          }
        }
      }
    }
  }
  .cartFooter {
    @media (max-width: 767px) {
      & {
        margin: 0;
      }
    }
  }
  .checkout-card-info-mobile {
    padding: 0;
    .userInfo {
      border-radius: 0 !important;
      .form-control {
        background: #fff;
        border: 1px solid #c1cdd9;
        height: 48px;
        border-radius: 8px;
        padding: 0 15px;
      }
    }
  }
  .checkout-table-block {
    padding: 0;
  }
  .std {
    tr {
      td:last-child {
        text-align: right !important;
      }
    }
  }
  .form-group {
    .form-control {
      padding: 5px 15px;
      height: 48px;
      border: 1px solid #cfd7e5;
      border-radius: 8px;
    }
  }
}
.mobile-checkout-title {
  .bg-secondary {
    @media (max-width: 767px) {
      display: none;
    }
  }
  @media (max-width: 767px) {
    position: relative;
    font-size: 16px;
    color: #000;
    border-bottom: solid 1px #dfe7f4;
    font-weight: 500;
  }
  //   &:after {
  //     position: absolute;
  //     content: "";
  //     width: 120%;
  //     height: 1px;
  //     left: -10%;
  //     right: 0;
  //     bottom: -12px;
  //     background-color: #dfe7f4;
  //   }
}
.mobile-checkout-footer {
  .btn {
    @media (max-width: 767px) {
      border-radius: 8px;
      width: 100%;
    }
  }
}
.mobile-checkout-table {
  @media (max-width: 767px) {
    padding: 0;
  }
  .table-button {
    .btn {
      @media (max-width: 767px) {
        border-radius: 8px;
        width: 100%;
      }
    }
  }
  .title-table {
    @media (max-width: 767px) {
      font-size: 16px;
      color: #000;
      font-weight: 500;
    }
  }
}
