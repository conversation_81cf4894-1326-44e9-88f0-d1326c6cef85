import React from "react";
import DealSlider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Link } from "react-router-dom";

const MostDealSectionStatic = () => {
  const sliderSettings = {
    infinite: true,
    autoplay: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };

  return (
    <section className="deal-container feature-product desktop">
      <div className="container">
        <div className="product-title common-section-title">
          <h2>Feature Products</h2>
        </div>
        <div className="row">
          <div className="col-lg-12 col-md-8">
            <section className="customer-logos slider">
              <DealSlider {...sliderSettings}>
                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$120 - <span className="oldprice">$150</span></div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$200</div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$150 - <span className="oldprice">$180</span></div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$100</div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$100</div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$100</div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="slide">
                  <div className="deal-link-item">
                    <Link to="">
                      <figure>
                        <a href="javascript:void(0)" className="wishlist-link">
                          <img src="/images/new-images/icon/wishlist-icon.svg" width="20" height="18" alt="wishlist" />
                        </a>
                        <img src="/images/camera.png" alt="Bloodhot Wireless speaker blue" />
                      </figure>
                      <div className="heading-wrap">
                        <h2>Bloodhot Wireless speaker blue</h2>
                        <div className="pricecart">
                          <div className="price">$100</div>
                          <button disabled>
                            <img src="/images/new-images/icon/top-cart.svg" width="20" height="16" alt="Cart" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>
              </DealSlider>
            </section>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MostDealSectionStatic;
