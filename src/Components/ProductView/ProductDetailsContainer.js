import React, {Component} from 'react';
import ProductImageCarausel from './ProductImageCarausel';
import ProductDetailsListArea from './ProductDetailsListArea';
import MobileProductDetailsListArea from './MobileProductDetailsListArea';

import {isMobile} from 'react-device-detect';

export default class ProductDetailsContainer extends Component {

    renderContent(){
        if (isMobile) {
            return <section className="listing-container product-view-container product-view-container--mobile">
                            <div className="container">
                                <div className="row">
                                    {/* <ProductImageCarausel /> */}
                                    <MobileProductDetailsListArea />
                                </div>
                            </div>
                        </section>
        }
        return <section className="listing-container product-view-container">
                        <div className="container">
                            <div className="row">
                                {/* <ProductImageCarausel /> */}
                                <ProductDetailsListArea />
                            </div>
                        </div>
                    </section>
    }
    render() {
        return this.renderContent();
    }
}