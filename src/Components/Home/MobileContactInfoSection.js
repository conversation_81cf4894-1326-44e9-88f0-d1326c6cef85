import React, { Component } from 'react';
import DealSlider from "react-slick";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import { Link } from 'react-router-dom';

export default class MobileContactInfoSection extends Component {
    render() {
        const mobilecontactinfo = {
            infinite: true,
            autoplay: true,
            speed: 500,
            slidesToShow: 1,
            slidesToScroll: 1,
            fade: false,
        };

        return (
            <section className="contact-info-container contact-info-container--mobile">
                <div className="container">
                    <div className="contact-info-wrap">
                        <div className="row">
                            <div className='col-md-12'>    
                                <DealSlider {...mobilecontactinfo}>
                                    <div className="mobile-info color1 d-flex align-items-center justify-content-start">
                                        <a href="mailto:<EMAIL>" className="mailinfo">
                                            <img src="images/new-images/icon/send-mail.svg" alt="Envíenos Un Correo" width="33" height="44"/>
                                            <div className="info-text">
                                                <h3>Envíanos un correo</h3>
                                                <p>Por favor no dude en enviarnos su consulta</p>
                                            </div>
                                        </a>
                                    </div>
                                    <div className="mobile-info color2 d-flex align-items-center justify-content-start">
                                        <div className="callinfo">
                                            <img src="images/new-images/icon/call-us-on.svg" alt="Llámanos al" width="33" height="44"/>
                                            <div className="info-text">
                                                <h3>Llámanos al</h3>
                                                <p>(************* / 302-1031 / 302-1032</p>
                                            </div>
                                        </div>
                                    </div>
                                </DealSlider>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        );
    }
}
