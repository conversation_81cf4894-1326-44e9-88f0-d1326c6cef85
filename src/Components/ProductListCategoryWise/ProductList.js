import React, { Component } from "react";
import BannerSection from "./BannerSection";
import BreadcrumbSection from "./BreadcrumbSection";
import ProductFilterSection from "./ProductFilterSection";
import ProductListingContainer from "./ProductListingContainer";
import { isMobile } from "react-device-detect";
import { MobileProductListMain } from "./MobileProductListMain";

export default class ProductList extends Component {
  render() {
    return (
      <>
        {(isMobile && <MobileProductListMain />) || (
          <div className="wraper">
            {/* <BannerSection /> */}
            <BreadcrumbSection />
            <ProductFilterSection />
            <ProductListingContainer />
          </div>
        )}
      </>
    );
  }
}
