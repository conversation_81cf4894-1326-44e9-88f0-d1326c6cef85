import React from "react";
import Accordion from "react-bootstrap/Accordion";
import { Button } from "react-bootstrap";
import { MobileProductListItem } from "./MobileProductListItem";

export const MobileProductListListing = ({
  products,
  subCategories,
  accordionActiveKey,
  setAccordionActiveKey,
  isLoading,
}) => {
  return (
    <div className="category-listing">
      {(isLoading && 
      <div className="text-center">
          <i className="fa fa-spinner fa-spin" style={{ fontSize: "24px" }}></i>
      </div>) ||
        (products?.length > 0 && (
          <>
            {/* If Category has Subcategories */}
            {(subCategories?.length > 0 && (
              <Accordion
                activeKey={accordionActiveKey}
                onSelect={(key) => setAccordionActiveKey(key)}
              >
                {subCategories?.map((subcategory) => (
                  <div className="accordion-item" key={subcategory?.id}>
                    <div
                      className={`accordion-header ${
                        accordionActiveKey === String(subcategory?.id)
                          ? "active"
                          : ""
                      }`}
                    >
                      <Accordion.Toggle
                        as={Button}
                        variant="link"
                        eventKey={String(subcategory?.id)}
                      >
                        <span>{subcategory?.name}</span>
                      </Accordion.Toggle>
                    </div>
                    <Accordion.Collapse eventKey={String(subcategory?.id)}>
                      <div className="accordion-body">
                        {(() => {
                          const filteredProducts = products?.filter(
                            (item) =>
                              item?.top_category_id == Number(subcategory?.id)
                          );

                          return filteredProducts?.length > 0 ? (
                            <div className="row">
                              {filteredProducts.map((filteredItem) => (
                                <div
                                  className="col-6"
                                  key={filteredItem?.product_id}
                                >
                                  <MobileProductListItem
                                    product={filteredItem}
                                  />
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="m-0">No product found.</p>
                          );
                        })()}
                      </div>
                    </Accordion.Collapse>
                  </div>
                ))}
              </Accordion>
            )) || (
              <>
                {/* If Category has no Subcategories */}
                <div className="py-2 px-3">
                  <div className="row">
                    {products.map((product) => (
                      <div className="col-6" key={product?.product_id}>
                        <MobileProductListItem product={product} />
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </>
        )) || <p className="px-3">No product found.</p>}
    </div>
  );
};
