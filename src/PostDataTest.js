import React from 'react';
import ReactDOM from 'react-dom';
import './index.css';
import * as serviceWorker from './serviceWorker';
import axios from 'axios';
import 'bootstrap/dist/css/bootstrap.min.css';

class Container extends React.Component {
  constructor(props) {
    super(props);
    // this.onChangeFirstName = this.onChangeFirstName.bind(this);
    // this.onChangeLastName = this.onChangeLastName.bind(this);
    // this.onChangeEmail = this.onChangeEmail.bind(this);
    this.handleOnSubmit = this.handleOnSubmit.bind(this);

    this.state = {
           fields: {},
           errors: {}
       }
  }

  handleFormValidation(){
    let fields = this.state.fields;
    let errors = {};
    let formIsValid = true;

    if(!fields["first_name"]){
       formIsValid = false;
       errors["first_name"] = "First name can not be empty";
    }

    if(typeof fields["first_name"] !== "undefined"){
       if(!fields["first_name"].match(/^[a-zA-Z]+$/)){
          formIsValid = false;
          errors["first_name"] = "First name accept only letters";
       }        
    }

    if(!fields["last_name"]){
       formIsValid = false;
       errors["last_name"] = "Last name can not be empty";
    }

    if(typeof fields["last_name"] !== "undefined"){
       if(!fields["last_name"].match(/^[a-zA-Z]+$/)){
          formIsValid = false;
          errors["last_name"] = "Last name accept only letters";
       }        
    }

    if(!fields["email"]){
       formIsValid = false;
       errors["email"] = "Email can not be empty";
    }

    if(typeof fields["email"] !== "undefined"){
       let lastAtPos = fields["email"].lastIndexOf('@');
       let lastDotPos = fields["email"].lastIndexOf('.');

       if (!(lastAtPos < lastDotPos && lastAtPos > 0 && fields["email"].indexOf('@@') === -1 && lastDotPos > 2 && (fields["email"].length - lastDotPos) > 2)) {
          formIsValid = false;
          errors["email"] = "Email is not valid";
        }
    }  

   this.setState({errors: errors});
   return formIsValid;
  }

  handleOnChange(field, e){         
    let fields = this.state.fields;
    fields[field] = e.target.value;        
    this.setState({fields});
  }

  handleOnSubmit(e){
    e.preventDefault();

    if(this.handleFormValidation()){
      const obj = {
      	first_name: this.state.fields.first_name,
      	last_name: this.state.fields.last_name,
      	email: this.state.fields.email,
      }

      var config = {
      	headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      };

      axios.post('http://localhost/custom/react/backend/insert.php', obj, config)
      .then(res => console.log(res.data));

      this.setState({fields:{['first_name'] : '', ['last_name'] : '', ['email'] : ''}});
    }

  }

  render() {
    return (
      <div className="container" style={{marginTop:10}}>
	      <h2>Form Submit with ReactJS & PHP</h2>
	      <form onSubmit={this.handleOnSubmit} >
	      <div className="form-group"><label>First Name:</label><input type="text" className="form-control" placeholder="Enter First Name"  value={this.state.fields.first_name || ''} onChange={this.handleOnChange.bind(this, "first_name")} /><span style={{color: "red"}}>{this.state.errors["first_name"]}</span></div>
	      <div className="form-group"><label>Last Name:</label><input type="text" className="form-control" placeholder="Enter Last Name" value={this.state.fields.last_name || ''} onChange={this.handleOnChange.bind(this, "last_name")} /><span style={{color: "red"}}>{this.state.errors["last_name"]}</span></div>
	      <div className="form-group"><label>Email Address:</label><input type="text" className="form-control" placeholder="Enter Email Id" value={this.state.fields.email || ''} onChange={this.handleOnChange.bind(this, "email")} /><span style={{color: "red"}}>{this.state.errors["email"]}</span></div>
	      <div className="form-group"><button type="submit" className="btn btn-success">Submit</button></div>
	      </form>
      </div>
    );
  }
}


ReactDOM.render(<Container />, document.getElementById('root'));

serviceWorker.unregister();
